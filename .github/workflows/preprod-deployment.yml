on:
  push:
    branches:
      - preprod-deployment-2
  workflow_dispatch:

name: Deploy to Preprod

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: preprod

    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.client_payload.branch }}

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: 'gradle'


      - name: Build with <PERSON>rad<PERSON>
        uses: gradle/gradle-build-action@67421db6bd0bf253fb4bd25b31ebb98943c375e1
        env:
          GITHUB_TOKEN: ${{ secrets.TOKEN }}
          GITHUB_USERNAME: ${{ secrets.TOKEN }}
        with:
          arguments: -x test build

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: pre_prod_ev_vehicle_be_repo
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f .docker/Dockerfile .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Update the image tag
        run: |
          sed -i'' -e 's/latest/${{ github.sha }}/g' manifests/preprod/ev-server-deployment.yml

      - name: Set up kubectl and kubecontext
        uses: azure/k8s-set-context@v1
        with:
          kubeconfig: ${{ secrets.KUBECONFIG_PREPROD }}

      - name: Deploy to Preprod
        run: |
          kubectl apply -f  manifests/preprod/ev-server-configmap.yml --insecure-skip-tls-verify
          kubectl apply -f  manifests/preprod/ev-server-sealed-secrets.yml --insecure-skip-tls-verify
          kubectl apply -f  manifests/preprod/ev-server-service.yml --insecure-skip-tls-verify
          kubectl apply -f  manifests/preprod/ev-server-deployment.yml --insecure-skip-tls-verify
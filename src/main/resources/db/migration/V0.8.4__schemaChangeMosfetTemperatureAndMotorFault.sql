ALTER TABLE vehicle_battery_data
ADD COLUMN IF NOT EXISTS mosfet_temperature REAL;

ALTER TABLE vehicle_battery_data_raw
ADD COLUMN IF NOT EXISTS mosfet_temperature REAL;
ALTER TABLE battery_alarm

ADD COLUMN IF NOT EXISTS part_type character varying(255) ,
DROP CONSTRAINT IF EXISTS battery_alarm_part_type_check,
ADD CONSTRAINT battery_alarm_part_type_check CHECK((part_type in ('BATTERY','CHARGER','MOTOR','COLOR','HORN','MIRROR','SPARKPLUG','SHOCK','STAND','TYRE','SAREE_GUARD','GPS','ACCELEROMETER','MCS','IO','DRIVE_MODE','TCU','CHASSIS','BMS','HS_DATA_LOGGER','VEHICLE','WHEEL','MCU','TCU_FIRMWARE','MCU_FIRMWARE','BMS_SOFTWARE'))),
DROP CONSTRAINT IF EXISTS battery_alarm_pkey,
ADD CONSTRAINT battery_alarm_pkey PRIMARY KEY ("timestamp", imei, alarm_type_id, part_type);

ALTER TABLE battery_alarm_raw
ADD COLUMN IF NOT EXISTS part_type character varying(255),
DROP CONSTRAINT IF EXISTS battery_alarm_raw_part_type_check,
ADD CONSTRAINT battery_alarm_raw_part_type_check CHECK((part_type in ('BATTERY','CHARGER','MOTOR','COLOR','HORN','MIRROR','SPARKPLUG','SHOCK','STAND','TYRE','SAREE_GUARD','GPS','ACCELEROMETER','MCS','IO','DRIVE_MODE','TCU','CHASSIS','BMS','HS_DATA_LOGGER','VEHICLE','WHEEL','MCU','TCU_FIRMWARE','MCU_FIRMWARE','BMS_SOFTWARE'))),
DROP CONSTRAINT IF EXISTS battery_alarm_raw_pkey,
ADD CONSTRAINT battery_alarm_raw_pkey PRIMARY KEY ("timestamp", imei, alarm_type_id, part_type);
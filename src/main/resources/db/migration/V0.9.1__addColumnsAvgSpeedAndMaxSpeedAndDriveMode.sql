-- Check if columns exist before altering table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'vehicle_running_metrics'
          AND column_name = 'avg_speed'
    ) THEN
        ALTER TABLE vehicle_running_metrics
        ADD COLUMN avg_speed FLOAT;
    END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'vehicle_running_metrics'
          AND column_name = 'max_speed'
    ) THEN
        ALTER TABLE vehicle_running_metrics
        ADD COLUMN max_speed FLOAT;
    END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'vehicle_running_metrics'
          AND column_name = 'drive_mode'
    ) THEN
        ALTER TABLE vehicle_running_metrics
        ADD COLUMN drive_mode VARCHAR(255);
        END IF;

       IF NOT EXISTS (
              SELECT 1
              FROM information_schema.columns
              WHERE table_name = 'vehicle_running_metrics'
                AND column_name = 'drive_mode_id'
          ) THEN
              ALTER TABLE vehicle_running_metrics
              ADD COLUMN drive_mode_id INTEGER;
          END IF;

          -- Check if foreign key constraint exists before adding
          IF NOT EXISTS (
              SELECT 1
              FROM information_schema.table_constraints
              WHERE table_name = 'vehicle_running_metrics'
                AND constraint_name = 'fk_drive_mode_id'
          ) THEN
              ALTER TABLE vehicle_running_metrics
              ADD CONSTRAINT fk_drive_mode_id
              FOREIGN KEY (drive_mode_id)
              REFERENCES drive_mode(id);
          END IF;
END $$;

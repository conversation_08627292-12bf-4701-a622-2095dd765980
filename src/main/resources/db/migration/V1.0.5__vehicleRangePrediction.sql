CREATE SEQUENCE IF NOT EXISTS prediction_model_seq start with 1 increment by 50;

CREATE TABLE IF NOT EXISTS prediction_model (
    id bigint PRIMARY KEY DEFAULT nextval('prediction_model_seq'),
	name VARCHAR(255),
	version VARCHAR(255),
	s3_path VARCHAR(1000),
    created_on timestamp(6) with time zone,
	updated_on timestamp(6) with time zone,
	host_header VARCHAR(300),
	rest_endpoint VARCHAR(1000),
	activated_on timestamp(6) with time zone,
	deactivated_on timestamp(6) with time zone,
	status VARCHAR(255) check(status in ('ACTIVE', 'INACTIVE','DELETED'))
);

CREATE INDEX IF NOT EXISTS prediction_model_name_idx ON prediction_model(name);

CREATE SEQUENCE IF NOT EXISTS vehicle_range_seq start with 1 increment by 50;

CREATE TABLE IF NOT EXISTS vehicle_range(
    id bigint PRIMARY KEY DEFAULT nextval('vehicle_range_seq'),
    vehicle_id bigint NOT NULL,
    imei character varying(255),
    timestamp timestamp(6) with time zone,
    range_correction double precision,
	drive_mode_id bigint not null,
    prediction_model_id bigint NOT NULL,
    constraint fk_vehicle_id FOREIGN KEY (vehicle_id) REFERENCES vehicle(id),
    constraint fk_prediction_model_id FOREIGN KEY (prediction_model_id) REFERENCES prediction_model(id),
    constraint fk_drive_mode_id FOREIGN KEY (drive_mode_id) REFERENCES drive_mode(id)
);
create index if not exists vehicle_range_imei_ts_idx on vehicle_range(imei, timestamp, drive_mode_id);
create index if not exists vehicle_range_vehicle_id_idx on vehicle_range(vehicle_id);
create index if not exists vehicle_range_drive_mode_idx on vehicle_range(drive_mode_id);


CREATE SEQUENCE IF NOT EXISTS vehicle_model_prediction_mapping_seq START WITH 1 INCREMENT BY 50;

CREATE TABLE IF NOT EXISTS vehicle_model_prediction_mapping (
    id BIGINT PRIMARY KEY DEFAULT nextval('vehicle_model_prediction_mapping_seq'),
    vehicle_model_id BIGINT NOT NULL,
    prediction_model_id BIGINT NOT NULL,
    created_on TIMESTAMP(6) WITH TIME ZONE NOT NULL,
    is_active BOOLEAN NOT NULL,
    CONSTRAINT fk_vehicle_model_id FOREIGN KEY (vehicle_model_id) REFERENCES vehicle_model(id),
    CONSTRAINT fk_prediction_model_id FOREIGN KEY (prediction_model_id) REFERENCES prediction_model(id)
);

CREATE INDEX IF NOT EXISTS vehicle_model_prediction_mapping_vehicle_model_id_idx ON vehicle_model_prediction_mapping(vehicle_model_id);
CREATE INDEX IF NOT EXISTS vehicle_model_prediction_mapping_prediction_model_id_idx ON vehicle_model_prediction_mapping(prediction_model_id);


WITH inserted_prediction_model AS (
    INSERT INTO prediction_model(name, version, s3_path, created_on, updated_on, host_header, rest_endpoint)
    VALUES ('range-prediction', '1.0.1', 's3://evahana.pretrained.models/preprod/v1/xgb_model_v1.0.1.json', now(), now(), 'range-prediction-dev.example.com', 'http://range-prediction-predictor.dev.svc.cluster.local')
    RETURNING id
)
INSERT INTO vehicle_model_prediction_mapping(vehicle_model_id, prediction_model_id, created_on, is_active)
SELECT vm.id AS vehicle_model_id, ipm.id AS prediction_model_id, now() AS created_on, true AS is_active
FROM vehicle_model vm, part_model pm, organisations o, inserted_prediction_model ipm
WHERE o.url_slug = 'nds' AND pm.mfr_org_id = o.id AND pm.id = vm.id;
--Continuous Aggregate for vehicle_battery_data across 1 minute window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_battery_first_aggregate_1m
WITH (timescaledb.continuous) AS
SELECT
    vbd.vehicle_id AS vehicle_id,
    vbd.imei AS imei,
    vbd.mfr_org_id AS mfr_org_id,
    vbd.owner_org_id AS owner_org_id,
    time_bucket('1m', vbd.timestamp) AS bucket,
    first(vbd.battery_volt, vbd.timestamp) FILTER (WHERE vbd.battery_volt IS NOT NULL) AS battery_volt,
    first(vbd.cell_volt_max, vbd.timestamp) FILTER (WHERE vbd.cell_volt_max IS NOT NULL) AS cell_volt_max,
    first(vbd.cell_volt_min, vbd.timestamp) FILTER (WHERE vbd.cell_volt_min IS NOT NULL) AS cell_volt_min,
    first(vbd.chg_cycle_count, vbd.timestamp) FILTER (WHERE vbd.chg_cycle_count IS NOT NULL) AS chg_cycle_count,
    first(vbd.current, vbd.timestamp) FILTER (WHERE vbd.current IS NOT NULL) AS current,
    first(vbd.dsg_cycle_count, vbd.timestamp) FILTER (WHERE vbd.dsg_cycle_count IS NOT NULL) AS dsg_cycle_count,
    first(vbd.soc, vbd.timestamp) FILTER (WHERE vbd.soc IS NOT NULL) AS soc,
    first(vbd.soh, vbd.timestamp) FILTER (WHERE vbd.soh IS NOT NULL) AS soh,
    first(vbd.temperature_max, vbd.timestamp) FILTER (WHERE vbd.temperature_max IS NOT NULL) AS temperature_max,
    first(vbd.temperature_min, vbd.timestamp) FILTER (WHERE vbd.temperature_min IS NOT NULL) AS temperature_min,
    first(vbd.remaining_capacity, vbd.timestamp) FILTER (WHERE vbd.remaining_capacity IS NOT NULL) AS remaining_capacity,
    first(vbd.mosfet_temperature, vbd.timestamp) FILTER (WHERE vbd.mosfet_temperature IS NOT NULL) AS mosfet_temperature
FROM vehicle_battery_data vbd
GROUP BY bucket, vbd.vehicle_id, vbd.imei, vbd.mfr_org_id, vbd.owner_org_id
WITH NO DATA;

--Refresh the vehicle_battery_first_aggregate_1m every minute
SELECT add_continuous_aggregate_policy('vehicle_battery_first_aggregate_1m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');


--Continuous Aggregate for vehicle_battery_data across 10 minutes window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_battery_first_aggregate_10m
WITH (timescaledb.continuous) AS
SELECT
    vbf1m.vehicle_id AS vehicle_id,
    vbf1m.imei AS imei,
    vbf1m.mfr_org_id AS mfr_org_id,
    vbf1m.owner_org_id AS owner_org_id,
    time_bucket('10m', vbf1m.bucket) AS bucket,
    first(vbf1m.battery_volt, vbf1m.bucket) FILTER (WHERE vbf1m.battery_volt IS NOT NULL) AS battery_volt,
    first(vbf1m.cell_volt_max, vbf1m.bucket) FILTER (WHERE vbf1m.cell_volt_max IS NOT NULL) AS cell_volt_max,
    first(vbf1m.cell_volt_min, vbf1m.bucket) FILTER (WHERE vbf1m.cell_volt_min IS NOT NULL) AS cell_volt_min,
    first(vbf1m.chg_cycle_count, vbf1m.bucket) FILTER (WHERE vbf1m.chg_cycle_count IS NOT NULL) AS chg_cycle_count,
    first(vbf1m.current, vbf1m.bucket) FILTER (WHERE vbf1m.current IS NOT NULL) AS current,
    first(vbf1m.dsg_cycle_count, vbf1m.bucket) FILTER (WHERE vbf1m.dsg_cycle_count IS NOT NULL) AS dsg_cycle_count,
    first(vbf1m.soc, vbf1m.bucket) FILTER (WHERE vbf1m.soc IS NOT NULL) AS soc,
    first(vbf1m.soh, vbf1m.bucket) FILTER (WHERE vbf1m.soh IS NOT NULL) AS soh,
    first(vbf1m.temperature_max, vbf1m.bucket) FILTER (WHERE vbf1m.temperature_max IS NOT NULL) AS temperature_max,
    first(vbf1m.temperature_min, vbf1m.bucket) FILTER (WHERE vbf1m.temperature_min IS NOT NULL) AS temperature_min,
    first(vbf1m.remaining_capacity, vbf1m.bucket) FILTER (WHERE vbf1m.remaining_capacity IS NOT NULL) AS remaining_capacity,
    first(vbf1m.mosfet_temperature, vbf1m.bucket) FILTER (WHERE vbf1m.mosfet_temperature IS NOT NULL) AS mosfet_temperature
FROM vehicle_battery_first_aggregate_1m vbf1m
GROUP BY time_bucket('10m', vbf1m.bucket), vbf1m.vehicle_id, vbf1m.imei, vbf1m.mfr_org_id, vbf1m.owner_org_id
WITH NO DATA;


--Refresh the vehicle_battery_first_aggregate_1m every 10 minutes
SELECT add_continuous_aggregate_policy('vehicle_battery_first_aggregate_10m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '10 minute');


--Continuous Aggregate for vehicle_battery_data across 1 hour window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_battery_first_aggregate_1h
WITH (timescaledb.continuous) AS
SELECT
    vbf10m.vehicle_id AS vehicle_id,
    vbf10m.imei AS imei,
    vbf10m.mfr_org_id AS mfr_org_id,
    vbf10m.owner_org_id AS owner_org_id,
    time_bucket('1h', vbf10m.bucket) AS bucket,
    first(vbf10m.battery_volt, vbf10m.bucket) FILTER (WHERE vbf10m.battery_volt IS NOT NULL) AS battery_volt,
    first(vbf10m.cell_volt_max, vbf10m.bucket) FILTER (WHERE vbf10m.cell_volt_max IS NOT NULL) AS cell_volt_max,
    first(vbf10m.cell_volt_min, vbf10m.bucket) FILTER (WHERE vbf10m.cell_volt_min IS NOT NULL) AS cell_volt_min,
    first(vbf10m.chg_cycle_count, vbf10m.bucket) FILTER (WHERE vbf10m.chg_cycle_count IS NOT NULL) AS chg_cycle_count,
    first(vbf10m.current, vbf10m.bucket) FILTER (WHERE vbf10m.current IS NOT NULL) AS current,
    first(vbf10m.dsg_cycle_count, vbf10m.bucket) FILTER (WHERE vbf10m.dsg_cycle_count IS NOT NULL) AS dsg_cycle_count,
    first(vbf10m.soc, vbf10m.bucket) FILTER (WHERE vbf10m.soc IS NOT NULL) AS soc,
    first(vbf10m.soh, vbf10m.bucket) FILTER (WHERE vbf10m.soh IS NOT NULL) AS soh,
    first(vbf10m.temperature_max, vbf10m.bucket) FILTER (WHERE vbf10m.temperature_max IS NOT NULL) AS temperature_max,
    first(vbf10m.temperature_min, vbf10m.bucket) FILTER (WHERE vbf10m.temperature_min IS NOT NULL) AS temperature_min,
    first(vbf10m.remaining_capacity, vbf10m.bucket) FILTER (WHERE vbf10m.remaining_capacity IS NOT NULL) AS remaining_capacity,
    first(vbf10m.mosfet_temperature, vbf10m.bucket) FILTER (WHERE vbf10m.mosfet_temperature IS NOT NULL) AS mosfet_temperature
FROM vehicle_battery_first_aggregate_10m vbf10m
GROUP BY time_bucket('1h', vbf10m.bucket), vbf10m.vehicle_id, vbf10m.imei, vbf10m.mfr_org_id, vbf10m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_battery_first_aggregate_1h every hour
SELECT add_continuous_aggregate_policy('vehicle_battery_first_aggregate_1h',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 hour');
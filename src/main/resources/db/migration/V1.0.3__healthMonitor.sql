CREATE TABLE motor_health_monitor (
    part_id BIGINT PRIMARY KEY,
    part_type VARCHAR(255),
    alarm_count INTEGER DEFAULT 0 ,
    alert_count INTEGER DEFAULT 0 ,
    updated_on TIMESTAMP(6) WITH TIME ZONE,
    CONS<PERSON><PERSON>IN<PERSON> monitor_health_part_type_check CHECK (part_type IN ('MOTOR', 'MCU'))
);

CREATE INDEX idx_motor_health_monitor_updated_on ON motor_health_monitor(updated_on);

CREATE INDEX idx_motor_health_monitor_part_type ON motor_health_monitor(part_type);


CREATE TABLE battery_health_monitor (
    part_id BIGINT PRIMARY KEY,
    part_type VARCHAR(255),
    alarm_count INTEGER DEFAULT 0,
    alert_count INTEGER DEFAULT 0,
    updated_on TIMESTAMP(6) WITH TIME ZONE,
    CONSTRAINT monitor_health_part_type_check CHECK (part_type IN ('BATTERY','BMS'))
);

CREATE INDEX idx_battery_health_monitor_updated_on ON battery_health_monitor(updated_on);

CREATE INDEX idx_battery_health_monitor_part_type ON battery_health_monitor(part_type);










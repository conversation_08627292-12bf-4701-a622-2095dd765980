-- Create the supported_tests table
CREATE TABLE test_type (
    id SERIAL PRIMARY KEY,
    test_type_name VARCHAR(255) UNIQUE,
    test_info TEXT
);
CREATE INDEX idx_test_type_test_type_name ON test_type (test_type_name);

-- Create the test_factors table, unique = true
CREATE TABLE test_factor (
    id SERIAL PRIMARY KEY,
    test_type VARCHAR(255),
    test_condition VARCHAR(255),
    code_module VARCHAR(255),
    CONSTRAINT fk_test_type FOREIGN KEY (test_type)
        REFERENCES test_type (test_type_name)
);
CREATE INDEX idx_test_factor_test_type ON test_factor (test_type);
CREATE INDEX idx_test_factor_test_condition ON test_factor (test_condition);

--Insert values into test_type
INSERT INTO test_type (test_type_name, test_info) VALUES
('CONNECTIVITY', 'Verifies network and communication functionality.'),
('DYNO', 'Measures performance metrics on a test bench.'),
('TEST_RIDE', 'Evaluates vehicle behavior during a test ride.');

-- Associate vehicle_test entity to supported_tests
ALTER TABLE vehicle_test
ADD CONSTRAINT fk_test_type FOREIGN KEY (test_type)
    REFERENCES test_type (test_type_name);
CREATE INDEX idx_vehicle_test_test_type ON vehicle_test (test_type);

--Insert values into test_factor
INSERT INTO test_factor (test_type, test_condition) VALUES
('CONNECTIVITY', 'OVERALL_DATA_AVAILABILITY'),
('CONNECTIVITY', 'TCU_DATA_AVAILABILITY'),
('CONNECTIVITY', 'MOTOR_DATA_AVAILABILITY'),
('CONNECTIVITY', 'LOCATION_DATA_AVAILABILITY'),
('CONNECTIVITY', 'BATTERY_DATA_AVAILABILITY'),
('CONNECTIVITY', 'LATEST_SOC'),
('CONNECTIVITY', 'MOTOR_CURRENT_IN_RANGE'),
('CONNECTIVITY', 'MOTOR_VOLTAGE_IN_RANGE'),
('CONNECTIVITY', 'DATA_LAG'),
('DYNO', 'DRIVE_MODE_SPEED');

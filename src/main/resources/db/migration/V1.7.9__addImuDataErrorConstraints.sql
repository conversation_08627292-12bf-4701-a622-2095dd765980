-- create constraints
ALTER TABLE evdata.vehicle_imu_data_error
ADD CONSTRAINT vehicle_imu_data_error_pk PRIMARY KEY (timestamp, imei);

ALTER TABLE evdata.vehicle_imu_data_error
ADD CONSTRAINT vehicle_imu_data_error_mfr_org_fk FOREIGN KEY (mfr_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_imu_data_error
ADD CONSTRAINT vehicle_imu_data_error_owner_org_fk FOREIGN KEY (owner_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_imu_data_error
ADD CONSTRAINT vehicle_imu_data_error_vehicle_fk FOREIGN KEY (vehicle_id)
REFERENCES evdata.vehicle (id);

CREATE INDEX vehicle_imu_data_error_id_idx ON evdata.vehicle_imu_data_error (imei,vehicle_id, timestamp);

-- create hypertable for imu data
SELECT create_hypertable('vehicle_imu_data_error', 'timestamp', migrate_data => true);

-- set chunk time interval for imu hypertables
SELECT set_chunk_time_interval('vehicle_imu_data_error', INTERVAL '7 days');

--set retention policy for imu hypertables
SELECT add_retention_policy('vehicle_imu_data_error', INTERVAL '3 months');
-- enable compression for imu hypertables
ALTER TABLE vehicle_imu_data_error
SET (
	timescaledb.compress,
	timescaledb.compress_segmentby = 'imei,vehicle_id,mfr_org_id,owner_org_id',
	timescaledb.compress_orderby='timestamp'
);

-- enable compression policies for imu hypertables
SELECT add_compression_policy('vehicle_imu_data_error', INTERVAL '1 days');

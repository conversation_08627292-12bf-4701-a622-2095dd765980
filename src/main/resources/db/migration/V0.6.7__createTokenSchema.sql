-- Table creation for Token
CREATE TABLE Token (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id BIGINT,
    token_type VARCHAR(10) CHECK (token_type IN ('BEARER', 'FCM')),
    token VARCHAR(1024),
    revoked BO<PERSON>EAN,
    expiry_date TIM<PERSON><PERSON><PERSON>,
    created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_token_user_id FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Index creation
CREATE INDEX token_idx ON Token(token);


--create sequence of token table
CREATE SEQUENCE token_seq
  START WITH 1
  INCREMENT BY 50
  NO MINVALUE
  NO MAXVALUE
  CACHE 50;


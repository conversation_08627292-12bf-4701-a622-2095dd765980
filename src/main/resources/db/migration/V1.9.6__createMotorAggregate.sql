DROP MATERIALIZED VIEW IF EXISTS evdata.vehicle_motor_first_aggregate_1d;
DROP MATERIALIZED VIEW IF EXISTS evdata.vehicle_motor_first_aggregate_1h;
DROP MATERIALIZED VIEW IF EXISTS evdata.vehicle_motor_first_aggregate_10m;
DROP MATERIALIZED VIEW IF EXISTS evdata.vehicle_motor_first_aggregate_1m;

--Continuous Aggregate for vehicle_telemetry_data(motor) across 1 minute window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_motor_first_aggregate_1m
WITH (timescaledb.continuous) AS
SELECT
    vtd.motor_id,
    vtd.vehicle_id AS vehicle_id,
    vtd.imei AS imei,
    vtd.mfr_org_id AS mfr_org_id,
    vtd.owner_org_id AS owner_org_id,
    time_bucket('1m', vtd.timestamp) AS bucket,
    first(vtd.motor_brake, vtd.timestamp) FILTER (WHERE vtd.motor_brake IS NOT NULL) AS motor_brake,
    first(vtd.motor_cruise, vtd.timestamp) FILTER (WHERE vtd.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vtd.motor_dc_current, vtd.timestamp) FILTER (WHERE vtd.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vtd.motor_dc_voltage, vtd.timestamp) FILTER (WHERE vtd.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vtd.motor_mcs_temperature, vtd.timestamp) FILTER (WHERE vtd.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vtd.motor_parking_sign, vtd.timestamp) FILTER (WHERE vtd.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vtd.motor_ready_sign, vtd.timestamp) FILTER (WHERE vtd.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vtd.motor_regeneration, vtd.timestamp) FILTER (WHERE vtd.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vtd.motor_reverse, vtd.timestamp) FILTER (WHERE vtd.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vtd.motor_side_stand, vtd.timestamp) FILTER (WHERE vtd.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vtd.motor_speed, vtd.timestamp) FILTER (WHERE vtd.motor_speed IS NOT NULL) AS motor_speed,
    first(vtd.motor_temperature, vtd.timestamp) FILTER (WHERE vtd.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vtd.motor_throttle, vtd.timestamp) FILTER (WHERE vtd.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vtd.motor_driving_mode, vtd.timestamp) FILTER (WHERE vtd.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vtd.motor_fault_feedback, vtd.timestamp) FILTER (WHERE vtd.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback,
    first(vtd.di_ignition, vtd.timestamp) FILTER (WHERE vtd.di_ignition IS NOT NULL) AS di_ignition,
    first(vtd.di_motion, vtd.timestamp) FILTER (WHERE vtd.di_motion IS NOT NULL) AS di_motion
FROM evdata.vehicle_motor_data vtd
GROUP BY bucket,vtd.motor_id, vtd.vehicle_id, vtd.imei, vtd.mfr_org_id, vtd.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1m every minute
SELECT add_continuous_aggregate_policy('evdata.vehicle_motor_first_aggregate_1m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');


--Continuous Aggregate for vehicle_telemetry_data(motor) across 10 minutes window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_motor_first_aggregate_10m
WITH (timescaledb.continuous) AS
SELECT
    vmf1m.motor_id,
    vmf1m.vehicle_id AS vehicle_id,
    vmf1m.imei AS imei,
    vmf1m.mfr_org_id AS mfr_org_id,
    vmf1m.owner_org_id AS owner_org_id,
    time_bucket('10m', vmf1m.bucket) AS bucket,
    first(vmf1m.motor_brake, vmf1m.bucket) FILTER (WHERE vmf1m.motor_brake IS NOT NULL) AS motor_brake,
    first(vmf1m.motor_cruise, vmf1m.bucket) FILTER (WHERE vmf1m.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vmf1m.motor_dc_current, vmf1m.bucket) FILTER (WHERE vmf1m.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vmf1m.motor_dc_voltage, vmf1m.bucket) FILTER (WHERE vmf1m.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vmf1m.motor_mcs_temperature, vmf1m.bucket) FILTER (WHERE vmf1m.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vmf1m.motor_parking_sign, vmf1m.bucket) FILTER (WHERE vmf1m.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vmf1m.motor_ready_sign, vmf1m.bucket) FILTER (WHERE vmf1m.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vmf1m.motor_regeneration, vmf1m.bucket) FILTER (WHERE vmf1m.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vmf1m.motor_reverse, vmf1m.bucket) FILTER (WHERE vmf1m.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vmf1m.motor_side_stand, vmf1m.bucket) FILTER (WHERE vmf1m.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vmf1m.motor_speed, vmf1m.bucket) FILTER (WHERE vmf1m.motor_speed IS NOT NULL) AS motor_speed,
    first(vmf1m.motor_temperature, vmf1m.bucket) FILTER (WHERE vmf1m.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vmf1m.motor_throttle, vmf1m.bucket) FILTER (WHERE vmf1m.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vmf1m.motor_driving_mode, vmf1m.bucket) FILTER (WHERE vmf1m.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vmf1m.motor_fault_feedback, vmf1m.bucket) FILTER (WHERE vmf1m.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback,
    first(vmf1m.di_ignition, vmf1m.bucket) FILTER (WHERE vmf1m.di_ignition IS NOT NULL) AS di_ignition,
    first(vmf1m.di_motion, vmf1m.bucket) FILTER (WHERE vmf1m.di_motion IS NOT NULL) AS di_motion
FROM evdata.vehicle_motor_first_aggregate_1m vmf1m
GROUP BY time_bucket('10m', vmf1m.bucket), vmf1m.motor_id, vmf1m.vehicle_id, vmf1m.imei, vmf1m.mfr_org_id, vmf1m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1m_1h every 10 minutes
SELECT add_continuous_aggregate_policy('evdata.vehicle_motor_first_aggregate_10m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '10 minute');


--Continuous Aggregate for vehicle_telemetry_data(motor) across 1 hour window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_motor_first_aggregate_1h
WITH (timescaledb.continuous) AS
SELECT
    vmf10m.motor_id,
    vmf10m.vehicle_id AS vehicle_id,
    vmf10m.imei AS imei,
    vmf10m.mfr_org_id AS mfr_org_id,
    vmf10m.owner_org_id AS owner_org_id,
    time_bucket('1h', vmf10m.bucket) AS bucket,
    first(vmf10m.motor_brake, vmf10m.bucket) FILTER (WHERE vmf10m.motor_brake IS NOT NULL) AS motor_brake,
    first(vmf10m.motor_cruise, vmf10m.bucket) FILTER (WHERE vmf10m.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vmf10m.motor_dc_current, vmf10m.bucket) FILTER (WHERE vmf10m.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vmf10m.motor_dc_voltage, vmf10m.bucket) FILTER (WHERE vmf10m.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vmf10m.motor_mcs_temperature, vmf10m.bucket) FILTER (WHERE vmf10m.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vmf10m.motor_parking_sign, vmf10m.bucket) FILTER (WHERE vmf10m.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vmf10m.motor_ready_sign, vmf10m.bucket) FILTER (WHERE vmf10m.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vmf10m.motor_regeneration, vmf10m.bucket) FILTER (WHERE vmf10m.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vmf10m.motor_reverse, vmf10m.bucket) FILTER (WHERE vmf10m.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vmf10m.motor_side_stand, vmf10m.bucket) FILTER (WHERE vmf10m.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vmf10m.motor_speed, vmf10m.bucket) FILTER (WHERE vmf10m.motor_speed IS NOT NULL) AS motor_speed,
    first(vmf10m.motor_temperature, vmf10m.bucket) FILTER (WHERE vmf10m.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vmf10m.motor_throttle, vmf10m.bucket) FILTER (WHERE vmf10m.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vmf10m.motor_driving_mode, vmf10m.bucket) FILTER (WHERE vmf10m.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vmf10m.motor_fault_feedback, vmf10m.bucket) FILTER (WHERE vmf10m.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback,
    first(vmf10m.di_ignition, vmf10m.bucket) FILTER (WHERE vmf10m.di_ignition IS NOT NULL) AS di_ignition,
    first(vmf10m.di_motion, vmf10m.bucket) FILTER (WHERE vmf10m.di_motion IS NOT NULL) AS di_motion
FROM evdata.vehicle_motor_first_aggregate_10m vmf10m
GROUP BY time_bucket('1h', vmf10m.bucket), vmf10m.motor_id, vmf10m.vehicle_id, vmf10m.imei, vmf10m.mfr_org_id, vmf10m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1h every hour
SELECT add_continuous_aggregate_policy('evdata.vehicle_motor_first_aggregate_1h',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 hour');


CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_motor_first_aggregate_1d
WITH (timescaledb.continuous) AS
SELECT
    vmf1h.motor_id,
    vmf1h.vehicle_id AS vehicle_id,
    vmf1h.imei AS imei,
    vmf1h.mfr_org_id AS mfr_org_id,
    vmf1h.owner_org_id AS owner_org_id,
    time_bucket('1d', vmf1h.bucket) AS bucket,
    first(vmf1h.motor_brake, vmf1h.bucket) FILTER (WHERE vmf1h.motor_brake IS NOT NULL) AS motor_brake,
    first(vmf1h.motor_cruise, vmf1h.bucket) FILTER (WHERE vmf1h.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vmf1h.motor_dc_current, vmf1h.bucket) FILTER (WHERE vmf1h.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vmf1h.motor_dc_voltage, vmf1h.bucket) FILTER (WHERE vmf1h.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vmf1h.motor_mcs_temperature, vmf1h.bucket) FILTER (WHERE vmf1h.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vmf1h.motor_parking_sign, vmf1h.bucket) FILTER (WHERE vmf1h.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vmf1h.motor_ready_sign, vmf1h.bucket) FILTER (WHERE vmf1h.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vmf1h.motor_regeneration, vmf1h.bucket) FILTER (WHERE vmf1h.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vmf1h.motor_reverse, vmf1h.bucket) FILTER (WHERE vmf1h.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vmf1h.motor_side_stand, vmf1h.bucket) FILTER (WHERE vmf1h.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vmf1h.motor_speed, vmf1h.bucket) FILTER (WHERE vmf1h.motor_speed IS NOT NULL) AS motor_speed,
    first(vmf1h.motor_temperature, vmf1h.bucket) FILTER (WHERE vmf1h.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vmf1h.motor_throttle, vmf1h.bucket) FILTER (WHERE vmf1h.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vmf1h.motor_driving_mode, vmf1h.bucket) FILTER (WHERE vmf1h.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vmf1h.motor_fault_feedback, vmf1h.bucket) FILTER (WHERE vmf1h.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback,
    first(vmf1h.di_ignition, vmf1h.bucket) FILTER (WHERE vmf1h.di_ignition IS NOT NULL) AS di_ignition,
    first(vmf1h.di_motion, vmf1h.bucket) FILTER (WHERE vmf1h.di_motion IS NOT NULL) AS di_motion
FROM evdata.vehicle_motor_first_aggregate_1h vmf1h
GROUP BY time_bucket('1d', vmf1h.bucket), vmf1h.motor_id, vmf1h.vehicle_id, vmf1h.imei, vmf1h.mfr_org_id, vmf1h.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1d every day
SELECT add_continuous_aggregate_policy('evdata.vehicle_motor_first_aggregate_1d',
  start_offset => INTERVAL '3 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 day');
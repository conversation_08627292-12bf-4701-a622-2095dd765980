alter table data_frequency_plan_details
rename feature_status to status;

update data_frequency_plan_details set unit='SECONDS';
alter table data_frequency_plan_details
add CONSTRAINT unit_check CHECK (unit::text = ANY (ARRAY['NANOS'::character varying::text, 'MICROS'::character varying::text,
														'MILLIS'::character varying::text, 'SECONDS'::character varying::text,
														'MINUTES'::character varying::text, 'HOURS'::character varying::text,
														'HALF_DAYS'::character varying::text, 'DAYS'::character varying::text,
														'WEEKS'::character varying::text, 'DECADES'::character varying::text,
														'CENTURIES'::character varying::text, 'MILLENNIA'::character varying::text,
														'ERAS'::character varying::text, 'FOREVER'::character varying::text]));

update data_frequency_plan set unit='SECONDS';
alter table data_frequency_plan
add CONSTRAINT unit_check CHECK (unit::text = ANY (ARRAY['NANOS'::character varying::text, 'MICROS'::character varying::text,
														'MILLIS'::character varying::text, 'SECONDS'::character varying::text,
														'MINUTES'::character varying::text, 'HOURS'::character varying::text,
														'HALF_DAYS'::character varying::text, 'DAYS'::character varying::text,
														'WEEKS'::character varying::text, 'DECADES'::character varying::text,
														'CENTURIES'::character varying::text, 'MILLENNIA'::character varying::text,
														'ERAS'::character varying::text, 'FOREVER'::character varying::text]));

update cron_frequency set unit='SECONDS';
alter table cron_frequency
add CONSTRAINT unit_check CHECK (unit::text = ANY (ARRAY['NANOS'::character varying::text, 'MICROS'::character varying::text,
														'MILLIS'::character varying::text, 'SECONDS'::character varying::text,
														'MINUTES'::character varying::text, 'HOURS'::character varying::text,
														'HALF_DAYS'::character varying::text, 'DAYS'::character varying::text,
														'WEEKS'::character varying::text, 'DECADES'::character varying::text,
														'CENTURIES'::character varying::text, 'MILLENNIA'::character varying::text,
														'ERAS'::character varying::text, 'FOREVER'::character varying::text]));
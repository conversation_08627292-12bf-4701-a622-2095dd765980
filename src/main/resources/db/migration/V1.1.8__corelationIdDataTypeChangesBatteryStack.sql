--Change TELEMETRY DATA corelation id data type to <PERSON><PERSON><PERSON> from varchar
ALTER TABLE battery_stack ADD COLUMN co_relation_id_temp UUID;
-- Update existing correlation id to temp table with datatype UUID with 5 days batch
-- DO $$
-- DECLARE
--     start_date TIMESTAMP := '2024-01-01 00:00:00';
--     end_date TIMESTAMP;
-- BEGIN
--     WHILE start_date < CURRENT_TIMESTAMP LOOP
--         end_date := start_date + INTERVAL '5 days';

--         -- Run the update for the current 5-day batch within a transaction
--         BEGIN
--             -- Start a new transaction for each batch
--             UPDATE battery_stack er
--             SET co_relation_id_temp = co_relation_id::UUID
--             WHERE er."timestamp" >= start_date
--             AND er."timestamp" < end_date;
--         EXCEPTION
--             WHEN OTHERS THEN
--                 -- Optionally, log or raise the error
--                 RAISE NOTICE 'Error in batch: % to %, rolled back.', start_date, end_date;
--         END;

--         -- Move to the next batch
--         start_date := end_date;
--     END LOOP;
-- END $$;

-- ALTER TABLE battery_stack DROP COLUMN co_relation_id;
-- ALTER TABLE battery_stack RENAME COLUMN co_relation_id_temp TO co_relation_id;
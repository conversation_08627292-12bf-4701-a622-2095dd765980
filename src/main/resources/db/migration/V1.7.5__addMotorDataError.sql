
DO $$
DECLARE
    start_date TIMESTAMP;
    end_date TIMESTAMP;
    current_start TIMESTAMP;
    current_end TIMESTAMP;
BEGIN
    -- Get date range
    SELECT MIN(timestamp), MAX(timestamp)
    INTO start_date, end_date
    FROM evdata.vehicle_telemetry_data_error;

    -- Initialize batch range
    current_start := start_date;
    current_end := start_date + INTERVAL '3 days';

    -- Loop through date range in 3-day batches
    WHILE current_start <= end_date LOOP
        -- Insert batch
        INSERT INTO evdata.vehicle_motor_data_error (
            motor_id,
            motor_brake,
            motor_cruise,
            motor_dc_current,
            motor_dc_voltage,
            motor_mcs_temperature,
            motor_parking_sign,
            motor_ready_sign,
            motor_regeneration,
            motor_reverse,
            motor_side_stand,
            motor_speed,
            motor_temperature,
            motor_throttle,
            motor_driving_mode,
            motor_fault_feedback,
            timestamp,
            imei,
            vehicle_id,
            created_on,
            mfr_org_id,
            owner_org_id,
            packet_received_on,
            co_relation_id,
            di_ignition,
            di_motion
        )
        SELECT
            p.id AS motor_id,
            vtd.motor_brake,
            vtd.motor_cruise,
            vtd.motor_dc_current,
            vtd.motor_dc_voltage,
            vtd.motor_mcs_temperature,
            vtd.motor_parking_sign,
            vtd.motor_ready_sign,
            vtd.motor_regeneration,
            vtd.motor_reverse,
            vtd.motor_side_stand,
            vtd.motor_speed,
            vtd.motor_temperature,
            vtd.motor_throttle,
            vtd.motor_driving_mode,
            vtd.motor_fault_feedback,
            vtd.timestamp,
            vtd.imei,
            vtd.vehicle_id,
            vtd.created_on,
            vtd.mfr_org_id,
            vtd.owner_org_id,
            vtd.packet_received_on,
            vtd.co_relation_id,
            di_ignition,
            di_motion
        FROM
            evdata.vehicle_telemetry_data_error vtd
            JOIN evdata.vehicle_parts vp ON vtd.vehicle_id = vp.vehicle_id
            JOIN evdata.part p ON vp.part_id = p.id
        WHERE
            p.part_type = 'MOTOR'
            AND vtd.timestamp >= current_start
            AND vtd.timestamp < current_end;

        -- Move to next 3-day batch
        current_start := current_end;
        current_end := current_end + INTERVAL '3 days';

    END LOOP;
END $$;
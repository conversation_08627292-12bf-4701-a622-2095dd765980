-- create constraints
ALTER TABLE evdata.vehicle_imu_data
ADD CONSTRAINT vehicle_imu_data_pk PRIMARY KEY (timestamp, imei);

ALTER TABLE evdata.vehicle_imu_data
ADD CONSTRAINT vehicle_imu_data_mfr_org_fk FOREIGN KEY (mfr_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_imu_data
ADD CONSTRAINT vehicle_imu_data_owner_org_fk FOREIGN KEY (owner_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_imu_data
ADD CONSTRAINT vehicle_imu_data_vehicle_fk FOREIGN KEY (vehicle_id)
REFERENCES evdata.vehicle (id);

CREATE INDEX vehicle_imu_data_owner_org_id_idx ON evdata.vehicle_imu_data (owner_org_id);
CREATE INDEX vehicle_imu_data_mfr_org_id_idx ON evdata.vehicle_imu_data (mfr_org_id);
CREATE INDEX vehicle_imu_data_id_idx ON evdata.vehicle_imu_data (imei, vehicle_id, timestamp);


-- create hypertable for motor data
SELECT create_hypertable('vehicle_imu_data', 'timestamp', migrate_data => true);


-- set chunk time interval for motor hypertables
SELECT set_chunk_time_interval('vehicle_imu_data', INTERVAL '7 days');

--set retention policy for motor hypertables
SELECT add_retention_policy('vehicle_imu_data', INTERVAL '3 months');
-- enable compression for motor hypertables
ALTER TABLE vehicle_imu_data
SET (
	timescaledb.compress,
	timescaledb.compress_segmentby = 'imei,vehicle_id,mfr_org_id,owner_org_id',
	timescaledb.compress_orderby='timestamp'
);
-- enable compression policies for motor hypertables
SELECT add_compression_policy('vehicle_imu_data', INTERVAL '1 days');

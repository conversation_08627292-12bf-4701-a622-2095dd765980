CREATE MATERIALIZED VIEW evdata.alarm_count_aggregate_1m
WITH (timescaledb.continuous)
AS
SELECT
    b.imei,
    b.part_type,
    b.vehicle_id,
    a.name AS alarm_name,
    COUNT(*) AS alarm_count,
    time_bucket('1 minute', b.timestamp) AS bucket
FROM evdata.battery_alarm AS b
JOIN evdata.alarm_type AS a ON b.alarm_type_id = a.id
GROUP BY b.imei, b.part_type, b.vehicle_id, a.name, time_bucket('1 minute', b.timestamp)
WITH NO DATA;


SELECT add_continuous_aggregate_policy('evdata.alarm_count_aggregate_1m',
  start_offset => INTERVAL '1 month',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');
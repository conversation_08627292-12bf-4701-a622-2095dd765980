-- create constraints
ALTER TABLE evdata.vehicle_motor_data
ADD CONSTRAINT vehicle_motor_data_pk PRIMARY KEY (timestamp, imei, motor_id);

ALTER TABLE evdata.vehicle_motor_data
ADD CONSTRAINT vehicle_motor_data_motor_fk FOREIGN KEY (motor_id)
REFERENCES evdata.part (id);

ALTER TABLE evdata.vehicle_motor_data
ADD CONSTRAINT vehicle_motor_data_mfr_org_fk FOREIGN KEY (mfr_org_id)
REFERENCES evusers.organisations (id);


ALTER TABLE evdata.vehicle_motor_data
ADD CONSTRAINT vehicle_motor_data_owner_org_fk FOREIGN KEY (owner_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_motor_data
ADD CONSTRAINT vehicle_motor_data_vehicle_fk FOREIGN KEY (vehicle_id)
REFERENCES evdata.vehicle (id);

-- create indexes

CREATE INDEX vehicle_motor_data_owner_org_id_idx ON evdata.vehicle_motor_data (owner_org_id);
CREATE INDEX vehicle_motor_data_mfr_org_id_idx ON evdata.vehicle_motor_data (mfr_org_id);
CREATE INDEX vehicle_motor_data_id_idx ON evdata.vehicle_motor_data (motor_id, imei,vehicle_id, timestamp);

-- create hypertable for motor data
SELECT create_hypertable('vehicle_motor_data', 'timestamp', migrate_data => true);

-- set chunk time interval for motor hypertables
SELECT set_chunk_time_interval('vehicle_motor_data', INTERVAL '7 days');

--set retention policy for motor hypertables
SELECT add_retention_policy('vehicle_motor_data', INTERVAL '3 months');
-- enable compression for motor hypertables
ALTER TABLE vehicle_motor_data
SET (
	timescaledb.compress,
	timescaledb.compress_segmentby = 'imei,motor_id,vehicle_id,mfr_org_id,owner_org_id',
	timescaledb.compress_orderby='timestamp'
);
-- enable compression policies for motor hypertables
SELECT add_compression_policy('vehicle_motor_data', INTERVAL '1 days');

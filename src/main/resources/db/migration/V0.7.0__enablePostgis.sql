ALTER TABLE vehicle_location_data
ADD COLUMN IF NOT EXISTS geo_point_zm GEOGRAPHY(POINTZM, 4326);
CREATE INDEX vehicle_loc_data_geo_point_zm_idx ON vehicle_location_data USING GIST (geo_point_zm);

ALTER TABLE vehicle_location_data_raw
ADD COLUMN IF NOT EXISTS geo_point_zm GEOGRAPHY(POINTZM, 4326);
CREATE INDEX vehicle_loc_data_raw_geo_point_zm_idx ON vehicle_location_data_raw USING GIST (geo_point_zm);
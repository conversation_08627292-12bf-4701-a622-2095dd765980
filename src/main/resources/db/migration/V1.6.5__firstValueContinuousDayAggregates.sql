--Continuous Aggregate for vehicle_telemetry_data across 1 day window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_telemetry_first_aggregate_1d
WITH (timescaledb.continuous) AS
SELECT
    vtf1h.vehicle_id AS vehicle_id,
    vtf1h.imei AS imei,
    vtf1h.mfr_org_id AS mfr_org_id,
    vtf1h.owner_org_id AS owner_org_id,
    time_bucket('1d', vtf1h.bucket) AS bucket,
    first(vtf1h.accel_x_axis, vtf1h.bucket) FILTER (WHERE vtf1h.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vtf1h.accel_y_axis, vtf1h.bucket) FILTER (WHERE vtf1h.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vtf1h.accel_z_axis, vtf1h.bucket) FILTER (WHERE vtf1h.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vtf1h.ai_lean_angle, vtf1h.bucket) FILTER (WHERE vtf1h.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vtf1h.ai_system_voltage, vtf1h.bucket) FILTER (WHERE vtf1h.ai_system_voltage IS NOT NULL) AS ai_system_voltage,
    first(vtf1h.ai_temperature, vtf1h.bucket) FILTER (WHERE vtf1h.ai_temperature IS NOT NULL) AS ai_temperature,
    first(vtf1h.ai_vbuck, vtf1h.bucket) FILTER (WHERE vtf1h.ai_vbuck IS NOT NULL) AS ai_vbuck,
    first(vtf1h.ai_voltage_input, vtf1h.bucket) FILTER (WHERE vtf1h.ai_voltage_input IS NOT NULL) AS ai_voltage_input,
    first(vtf1h.ai_vusr1, vtf1h.bucket) FILTER (WHERE vtf1h.ai_vusr1 IS NOT NULL) AS ai_vusr1,
    first(vtf1h.ai_vusr2, vtf1h.bucket) FILTER (WHERE vtf1h.ai_vusr2 IS NOT NULL) AS ai_vusr2,
    first(vtf1h.di_ignition, vtf1h.bucket) FILTER (WHERE vtf1h.di_ignition IS NOT NULL) AS di_ignition,
    first(vtf1h.di_main_power, vtf1h.bucket) FILTER (WHERE vtf1h.di_main_power IS NOT NULL) AS di_main_power,
    first(vtf1h.di_motion, vtf1h.bucket) FILTER (WHERE vtf1h.di_motion IS NOT NULL) AS di_motion,
    first(vtf1h.di_tamper, vtf1h.bucket) FILTER (WHERE vtf1h.di_tamper IS NOT NULL) AS di_tamper,
    first(vtf1h.di_usr1, vtf1h.bucket) FILTER (WHERE vtf1h.di_usr1 IS NOT NULL) AS di_usr1,
    first(vtf1h.di_usr2, vtf1h.bucket) FILTER (WHERE vtf1h.di_usr2 IS NOT NULL) AS di_usr2,
    first(vtf1h.do_usr1, vtf1h.bucket) FILTER (WHERE vtf1h.do_usr1 IS NOT NULL) AS do_usr1,
    first(vtf1h.do_usr2, vtf1h.bucket) FILTER (WHERE vtf1h.do_usr2 IS NOT NULL) AS do_usr2,
    first(vtf1h.gyro_x_axis, vtf1h.bucket) FILTER (WHERE vtf1h.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vtf1h.gyro_y_axis, vtf1h.bucket) FILTER (WHERE vtf1h.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vtf1h.gyro_z_axis, vtf1h.bucket) FILTER (WHERE vtf1h.gyro_z_axis IS NOT NULL) AS gyro_z_axis,
    first(vtf1h.grv_x_axis, vtf1h.bucket) FILTER (WHERE vtf1h.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vtf1h.grv_y_axis, vtf1h.bucket) FILTER (WHERE vtf1h.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vtf1h.grv_z_axis, vtf1h.bucket) FILTER (WHERE vtf1h.grv_z_axis IS NOT NULL) AS grv_z_axis
FROM vehicle_telemetry_first_aggregate_1h vtf1h
GROUP BY time_bucket('1d', vtf1h.bucket), vtf1h.vehicle_id, vtf1h.imei, vtf1h.mfr_org_id, vtf1h.owner_org_id
WITH NO DATA;

--Refresh the vehicle_telemetry_first_aggregate_1d every day
SELECT add_continuous_aggregate_policy('vehicle_telemetry_first_aggregate_1d',
  start_offset => INTERVAL '3 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 day');
  
  

--Continuous Aggregate for vehicle_telemetry_data(motor) across 1 day window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_motor_first_aggregate_1d
WITH (timescaledb.continuous) AS
SELECT
    vmf1h.vehicle_id AS vehicle_id,
    vmf1h.imei AS imei,
    vmf1h.mfr_org_id AS mfr_org_id,
    vmf1h.owner_org_id AS owner_org_id,
    time_bucket('1d', vmf1h.bucket) AS bucket,
    first(vmf1h.motor_brake, vmf1h.bucket) FILTER (WHERE vmf1h.motor_brake IS NOT NULL) AS motor_brake,
    first(vmf1h.motor_cruise, vmf1h.bucket) FILTER (WHERE vmf1h.motor_cruise IS NOT NULL) AS motor_cruise,
    first(vmf1h.motor_dc_current, vmf1h.bucket) FILTER (WHERE vmf1h.motor_dc_current IS NOT NULL) AS motor_dc_current,
    first(vmf1h.motor_dc_voltage, vmf1h.bucket) FILTER (WHERE vmf1h.motor_dc_voltage IS NOT NULL) AS motor_dc_voltage,
    first(vmf1h.motor_mcs_temperature, vmf1h.bucket) FILTER (WHERE vmf1h.motor_mcs_temperature IS NOT NULL) AS motor_mcs_temperature,
    first(vmf1h.motor_parking_sign, vmf1h.bucket) FILTER (WHERE vmf1h.motor_parking_sign IS NOT NULL) AS motor_parking_sign,
    first(vmf1h.motor_ready_sign, vmf1h.bucket) FILTER (WHERE vmf1h.motor_ready_sign IS NOT NULL) AS motor_ready_sign,
    first(vmf1h.motor_regeneration, vmf1h.bucket) FILTER (WHERE vmf1h.motor_regeneration IS NOT NULL) AS motor_regeneration,
    first(vmf1h.motor_reverse, vmf1h.bucket) FILTER (WHERE vmf1h.motor_reverse IS NOT NULL) AS motor_reverse,
    first(vmf1h.motor_side_stand, vmf1h.bucket) FILTER (WHERE vmf1h.motor_side_stand IS NOT NULL) AS motor_side_stand,
    first(vmf1h.motor_speed, vmf1h.bucket) FILTER (WHERE vmf1h.motor_speed IS NOT NULL) AS motor_speed,
    first(vmf1h.motor_temperature, vmf1h.bucket) FILTER (WHERE vmf1h.motor_temperature IS NOT NULL) AS motor_temperature,
    first(vmf1h.motor_throttle, vmf1h.bucket) FILTER (WHERE vmf1h.motor_throttle IS NOT NULL) AS motor_throttle,
    first(vmf1h.motor_driving_mode, vmf1h.bucket) FILTER (WHERE vmf1h.motor_driving_mode IS NOT NULL) AS motor_driving_mode,
    first(vmf1h.motor_fault_feedback, vmf1h.bucket) FILTER (WHERE vmf1h.motor_fault_feedback IS NOT NULL) AS motor_fault_feedback
FROM vehicle_motor_first_aggregate_1h vmf1h
GROUP BY time_bucket('1d', vmf1h.bucket), vmf1h.vehicle_id, vmf1h.imei, vmf1h.mfr_org_id, vmf1h.owner_org_id
WITH NO DATA;

--Refresh the vehicle_motor_first_aggregate_1d every day
SELECT add_continuous_aggregate_policy('vehicle_motor_first_aggregate_1d',
  start_offset => INTERVAL '3 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 day');
  
  
 
 --Continuous Aggregate for vehicle_battery_data across 1 day window with first non null value
 CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_battery_first_aggregate_1d
 WITH (timescaledb.continuous) AS
 SELECT
     vbf1h.vehicle_id AS vehicle_id,
     vbf1h.imei AS imei,
     vbf1h.mfr_org_id AS mfr_org_id,
     vbf1h.owner_org_id AS owner_org_id,
     time_bucket('1d', vbf1h.bucket) AS bucket,
     first(vbf1h.battery_volt, vbf1h.bucket) FILTER (WHERE vbf1h.battery_volt IS NOT NULL) AS battery_volt,
     first(vbf1h.cell_volt_max, vbf1h.bucket) FILTER (WHERE vbf1h.cell_volt_max IS NOT NULL) AS cell_volt_max,
     first(vbf1h.cell_volt_min, vbf1h.bucket) FILTER (WHERE vbf1h.cell_volt_min IS NOT NULL) AS cell_volt_min,
     first(vbf1h.chg_cycle_count, vbf1h.bucket) FILTER (WHERE vbf1h.chg_cycle_count IS NOT NULL) AS chg_cycle_count,
     first(vbf1h.current, vbf1h.bucket) FILTER (WHERE vbf1h.current IS NOT NULL) AS current,
     first(vbf1h.dsg_cycle_count, vbf1h.bucket) FILTER (WHERE vbf1h.dsg_cycle_count IS NOT NULL) AS dsg_cycle_count,
     first(vbf1h.soc, vbf1h.bucket) FILTER (WHERE vbf1h.soc IS NOT NULL) AS soc,
     first(vbf1h.soh, vbf1h.bucket) FILTER (WHERE vbf1h.soh IS NOT NULL) AS soh,
     first(vbf1h.temperature_max, vbf1h.bucket) FILTER (WHERE vbf1h.temperature_max IS NOT NULL) AS temperature_max,
     first(vbf1h.temperature_min, vbf1h.bucket) FILTER (WHERE vbf1h.temperature_min IS NOT NULL) AS temperature_min,
     first(vbf1h.remaining_capacity, vbf1h.bucket) FILTER (WHERE vbf1h.remaining_capacity IS NOT NULL) AS remaining_capacity,
     first(vbf1h.mosfet_temperature, vbf1h.bucket) FILTER (WHERE vbf1h.mosfet_temperature IS NOT NULL) AS mosfet_temperature
 FROM vehicle_battery_first_aggregate_1h vbf1h
 GROUP BY time_bucket('1d', vbf1h.bucket), vbf1h.vehicle_id, vbf1h.imei, vbf1h.mfr_org_id, vbf1h.owner_org_id
 WITH NO DATA;
 
 --Refresh the vehicle_battery_first_aggregate_1d every hour
 SELECT add_continuous_aggregate_policy('vehicle_battery_first_aggregate_1d',
   start_offset => INTERVAL '3 day',
   end_offset => INTERVAL '1 minute',
   schedule_interval => INTERVAL '1 day');
   
   
 
 --Continuous Aggregate for vehicle_location_data across 1 day window with first non null value
 CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_location_first_aggregate_1d
 WITH (timescaledb.continuous) AS
 SELECT
     vlf1h.vehicle_id AS vehicle_id,
     vlf1h.imei AS imei,
     vlf1h.mfr_org_id AS mfr_org_id,
     vlf1h.owner_org_id AS owner_org_id,
     time_bucket('1d', vlf1h.bucket) AS bucket,
     first(vlf1h.altitude, vlf1h.bucket) FILTER (WHERE vlf1h.altitude IS NOT NULL) AS altitude,
     first(vlf1h.brg, vlf1h.bucket) FILTER (WHERE vlf1h.brg IS NOT NULL) AS brg,
     first(vlf1h.hdop, vlf1h.bucket) FILTER (WHERE vlf1h.hdop IS NOT NULL) AS hdop,
     first(vlf1h.latitude, vlf1h.bucket) FILTER (WHERE vlf1h.latitude IS NOT NULL) AS latitude,
     first(vlf1h.longitude, vlf1h.bucket) FILTER (WHERE vlf1h.longitude IS NOT NULL) AS longitude,
     first(vlf1h.pdop, vlf1h.bucket) FILTER (WHERE vlf1h.pdop IS NOT NULL) AS pdop,
     first(vlf1h.speed, vlf1h.bucket) FILTER (WHERE vlf1h.speed IS NOT NULL) AS speed,
     first(vlf1h.track_sats, vlf1h.bucket) FILTER (WHERE vlf1h.track_sats IS NOT NULL) AS track_sats,
     first(vlf1h.vdop, vlf1h.bucket) FILTER (WHERE vlf1h.vdop IS NOT NULL) AS vdop,
     first(vlf1h.view_sats, vlf1h.bucket) FILTER (WHERE vlf1h.view_sats IS NOT NULL) AS view_sats,
     first(vlf1h.geo_point_zm, vlf1h.bucket) FILTER (WHERE vlf1h.geo_point_zm IS NOT NULL) AS geo_point_zm
 FROM vehicle_location_first_aggregate_1h vlf1h
 GROUP BY time_bucket('1d', vlf1h.bucket), vlf1h.vehicle_id, vlf1h.imei, vlf1h.mfr_org_id, vlf1h.owner_org_id
 WITH NO DATA;
 
 --Refresh the vehicle_location_first_aggregate_1d every hour
 SELECT add_continuous_aggregate_policy('vehicle_location_first_aggregate_1d',
   start_offset => INTERVAL '3 day',
   end_offset => INTERVAL '1 minute',
   schedule_interval => INTERVAL '1 day');
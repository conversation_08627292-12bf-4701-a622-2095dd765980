--creating MATERIALIZED VIEW for vehicle_running_metrics
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_running_metrics_aggregate
WITH (timescaledb.continuous) AS
select
vrm.imei as imei,
time_bucket ('10s',vrm.timestamp) as time_bucket,
sum(vrm.distance_travelled) AS distance_travelled,
count(vrm.distance_travelled) * 10 AS ride_duration,
mode() WITHIN GROUP (ORDER BY vrm.cal_soc) AS cal_soc,
mode() WITHIN GROUP (ORDER BY vrm.soc) AS soc,
sum(vrm.discharge) AS discharge,
sum(vrm.cal_discharge) AS cal_discharge,
avg(vrm.avg_speed) AS avg_speed,
max(vrm.max_speed) AS max_speed,
mode() WITHIN GROUP (ORDER BY vrm.drive_mode_id) AS drive_mode_id
from vehicle_running_metrics vrm
where vrm.timestamp > '2024-01-01'
group by vrm.imei,time_bucket,time_bucket('10s',vrm.timestamp) WITH NO DATA;

--again adding policy with refresh view at midnight
SELECT add_continuous_aggregate_policy(
    'vehicle_running_metrics_aggregate',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '0 seconds',
    schedule_interval => INTERVAL '1 day',
    initial_start => date_trunc('day', now()) + INTERVAL '1 day'
);


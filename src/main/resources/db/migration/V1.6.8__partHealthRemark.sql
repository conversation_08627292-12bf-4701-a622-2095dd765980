CREATE SEQUENCE IF NOT EXISTS part_health_limit_id_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE IF NOT EXISTS part_health_limit (
    id BIGINT PRIMARY KEY DEFAULT nextval('part_health_limit_id_seq'),
    part_type VARCHAR(255) NOT NULL,
    remark VARCHAR(255) NOT NULL,
    value INTEGER NOT NULL
);



   INSERT INTO part_health_limit (part_type , remark , value) VALUES
   ('MOTOR' , 'Excellent' ,0),
   ('MOTOR' , 'Good' ,1),
   ('MOTOR' , 'Average' ,2),
   ('MOTOR' , 'Poor' ,3);

   INSERT INTO part_health_limit (part_type , remark , value) VALUES
   ('BATTERY' , 'Excellent' ,0),
   ('BATTERY' , 'Good' ,1),
   ('BATTERY' , 'Average' ,2),
   ('BATTERY' , 'Poor' ,3);

   INSERT INTO part_health_limit (part_type , remark , value) VALUES
   ('REAR_TYRE' , 'Low' ,0),
   ('REAR_TYRE' , 'Normal' ,1),
   ('REAR_TYRE' , 'High' ,2);

CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.median_grv_still_1day_aggregate
 WITH (timescaledb.continuous) AS

	SELECT
		imei,
    	time_bucket('1d', timestamp) AS bucket,
		percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_x_axis) AS grv_x_still,
    	percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_y_axis) AS grv_y_still,
    	percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_z_axis) AS grv_z_still
		FROM evdata.vehicle_imu_data
		WHERE di_ignition = false and di_motion = false and timestamp > '2024-12-02'
	GROUP BY imei,time_bucket ('1d',timestamp) WITH NO DATA;

SELECT add_continuous_aggregate_policy('evdata.median_grv_still_1day_aggregate',
  start_offset => INTERVAL '3 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '25 minute');



CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.median_grv_running_1day_aggregate
   WITH (timescaledb.continuous) AS

  	SELECT
  		imei,
      	time_bucket('1d', timestamp) AS bucket,
  		percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_x_axis) AS grv_x_running,
      	percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_y_axis) AS grv_y_running,
      	percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_z_axis) AS grv_z_running
  		FROM evdata.vehicle_imu_data
  		WHERE di_ignition = true and di_motion = true and timestamp > '2024-12-02'
  	GROUP BY imei,time_bucket ('1d',timestamp) WITH NO DATA;

  SELECT add_continuous_aggregate_policy('evdata.median_grv_running_1day_aggregate',
    start_offset => INTERVAL '3 day',
    end_offset => INTERVAL '1 minute',
    schedule_interval => INTERVAL '25 minute');



CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_imu_first_aggregate_1m
WITH (timescaledb.continuous) AS
SELECT
    vid.vehicle_id AS vehicle_id,
    vid.imei AS imei,
    vid.mfr_org_id AS mfr_org_id,
    vid.owner_org_id AS owner_org_id,
    time_bucket('1m', vid.timestamp) AS bucket,
    first(vid.ai_lean_angle, vid.timestamp) FILTER (WHERE vid.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vid.accel_x_axis, vid.timestamp) FILTER (WHERE vid.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vid.accel_y_axis, vid.timestamp) FILTER (WHERE vid.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vid.accel_z_axis, vid.timestamp) FILTER (WHERE vid.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vid.grv_x_axis, vid.timestamp) FILTER (WHERE vid.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vid.grv_y_axis, vid.timestamp) FILTER (WHERE vid.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vid.grv_z_axis, vid.timestamp) FILTER (WHERE vid.grv_z_axis IS NOT NULL) AS grv_z_axis,
    first(vid.gyro_x_axis, vid.timestamp) FILTER (WHERE vid.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vid.gyro_y_axis, vid.timestamp) FILTER (WHERE vid.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vid.gyro_z_axis, vid.timestamp) FILTER (WHERE vid.gyro_z_axis IS NOT NULL) AS gyro_z_axis
FROM evdata.vehicle_imu_data vid
GROUP BY
    time_bucket('1m', vid.timestamp),
    vid.vehicle_id,
    vid.imei,
    vid.mfr_org_id,
    vid.owner_org_id
WITH NO DATA;

--Refresh the vehicle_imu_first_aggregate_1m every minute
SELECT add_continuous_aggregate_policy('evdata.vehicle_imu_first_aggregate_1m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');


--Continuous Aggregate for vehicle_imu_data across 10 minutes window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_imu_first_aggregate_10m
WITH (timescaledb.continuous) AS
SELECT
    vid.vehicle_id AS vehicle_id,
    vid.imei AS imei,
    vid.mfr_org_id AS mfr_org_id,
    vid.owner_org_id AS owner_org_id,
    time_bucket('10m', vid.timestamp) AS bucket,
    first(vid.ai_lean_angle, vid.timestamp) FILTER (WHERE vid.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vid.accel_x_axis, vid.timestamp) FILTER (WHERE vid.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vid.accel_y_axis, vid.timestamp) FILTER (WHERE vid.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vid.accel_z_axis, vid.timestamp) FILTER (WHERE vid.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vid.grv_x_axis, vid.timestamp) FILTER (WHERE vid.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vid.grv_y_axis, vid.timestamp) FILTER (WHERE vid.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vid.grv_z_axis, vid.timestamp) FILTER (WHERE vid.grv_z_axis IS NOT NULL) AS grv_z_axis,
    first(vid.gyro_x_axis, vid.timestamp) FILTER (WHERE vid.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vid.gyro_y_axis, vid.timestamp) FILTER (WHERE vid.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vid.gyro_z_axis, vid.timestamp) FILTER (WHERE vid.gyro_z_axis IS NOT NULL) AS gyro_z_axis
FROM evdata.vehicle_imu_data vid
GROUP BY
    time_bucket('10m', vid.timestamp),
    vid.vehicle_id,
    vid.imei,
    vid.mfr_org_id,
    vid.owner_org_id
WITH NO DATA;

--Refresh the vehicle_imu_first_aggregate_1m every 10 minutes
SELECT add_continuous_aggregate_policy('evdata.vehicle_imu_first_aggregate_10m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '10 minute');


--Continuous Aggregate for vehicle_imu_data across 1 hour window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_imu_first_aggregate_1h
WITH (timescaledb.continuous) AS
SELECT
    vid.vehicle_id AS vehicle_id,
    vid.imei AS imei,
    vid.mfr_org_id AS mfr_org_id,
    vid.owner_org_id AS owner_org_id,
    time_bucket('1h', vid.timestamp) AS bucket,
    first(vid.ai_lean_angle, vid.timestamp) FILTER (WHERE vid.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vid.accel_x_axis, vid.timestamp) FILTER (WHERE vid.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vid.accel_y_axis, vid.timestamp) FILTER (WHERE vid.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vid.accel_z_axis, vid.timestamp) FILTER (WHERE vid.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vid.grv_x_axis, vid.timestamp) FILTER (WHERE vid.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vid.grv_y_axis, vid.timestamp) FILTER (WHERE vid.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vid.grv_z_axis, vid.timestamp) FILTER (WHERE vid.grv_z_axis IS NOT NULL) AS grv_z_axis,
    first(vid.gyro_x_axis, vid.timestamp) FILTER (WHERE vid.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vid.gyro_y_axis, vid.timestamp) FILTER (WHERE vid.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vid.gyro_z_axis, vid.timestamp) FILTER (WHERE vid.gyro_z_axis IS NOT NULL) AS gyro_z_axis
FROM evdata.vehicle_imu_data vid
GROUP BY
    time_bucket('1h', vid.timestamp),
    vid.vehicle_id,
    vid.imei,
    vid.mfr_org_id,
    vid.owner_org_id
WITH NO DATA;

--Refresh the vehicle_imu_first_aggregate_1h every hour
SELECT add_continuous_aggregate_policy('evdata.vehicle_imu_first_aggregate_1h',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 hour');

--Continuous Aggregate for vehicle_imu_data across 1 day window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS evdata.vehicle_imu_first_aggregate_1d
WITH (timescaledb.continuous) AS
SELECT
    vid.vehicle_id AS vehicle_id,
    vid.imei AS imei,
    vid.mfr_org_id AS mfr_org_id,
    vid.owner_org_id AS owner_org_id,
    time_bucket('1d', vid.timestamp) AS bucket,
    first(vid.ai_lean_angle, vid.timestamp) FILTER (WHERE vid.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vid.accel_x_axis, vid.timestamp) FILTER (WHERE vid.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vid.accel_y_axis, vid.timestamp) FILTER (WHERE vid.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vid.accel_z_axis, vid.timestamp) FILTER (WHERE vid.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vid.grv_x_axis, vid.timestamp) FILTER (WHERE vid.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vid.grv_y_axis, vid.timestamp) FILTER (WHERE vid.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vid.grv_z_axis, vid.timestamp) FILTER (WHERE vid.grv_z_axis IS NOT NULL) AS grv_z_axis,
    first(vid.gyro_x_axis, vid.timestamp) FILTER (WHERE vid.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vid.gyro_y_axis, vid.timestamp) FILTER (WHERE vid.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vid.gyro_z_axis, vid.timestamp) FILTER (WHERE vid.gyro_z_axis IS NOT NULL) AS gyro_z_axis
FROM evdata.vehicle_imu_data vid
GROUP BY
    time_bucket('1d', vid.timestamp),
    vid.vehicle_id,
    vid.imei,
    vid.mfr_org_id,
    vid.owner_org_id
WITH NO DATA;

--Refresh the vehicle_imu_first_aggregate_1d every day
SELECT add_continuous_aggregate_policy('evdata.vehicle_imu_first_aggregate_1d',
  start_offset => INTERVAL '3 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 day');


  -- Create the materialized vehicle_imu_data_availability_aggregate_daily
  CREATE MATERIALIZED VIEW evdata.vehicle_imu_data_availability_aggregate_daily
  WITH (timescaledb.continuous) AS
  SELECT
      vehicle_id,
      imei,
      time_bucket('1 day', timestamp) AS day,
      COUNT(*) AS imu_data_availability
  FROM
      evdata.vehicle_imu_data
  GROUP BY
      vehicle_id,
      imei,
      day
  WITH NO DATA;

  CREATE INDEX imu_availability_idx_vehicle_id
  ON evdata.vehicle_imu_data_availability_aggregate_daily (vehicle_id);

  CREATE INDEX imu_availability_idx_day_imei_telemetry
  ON evdata.vehicle_imu_data_availability_aggregate_daily (day, imei, imu_data_availability);

  SELECT add_continuous_aggregate_policy('evdata.vehicle_imu_data_availability_aggregate_daily',
      start_offset => INTERVAL '2 day',
      end_offset => INTERVAL '0 day',
      schedule_interval => INTERVAL '5 minutes');

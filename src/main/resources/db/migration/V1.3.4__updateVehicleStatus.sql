alter table vehicle_status
add column update_source varchar,
add CONSTRAINT update_source_check CHECK (update_source::text = ANY (ARRAY['STATUS_EVENT'::character varying::text, 'DATA_DELAY_EVENT'::character varying::text]));

update data_frequency_plan_details
set feature_name ='VEHICLE_STATUS';

alter table data_frequency_plan_details
add CONSTRAINT feature_name_check CHECK (feature_name::text = ANY (ARRAY['VEHICLE_STATUS'::character varying::text, 'VEHICLE_STATUS_UPDATE'::character varying::text]));

--data insertion into data_frequency_plan_details
INSERT INTO data_frequency_plan_details (feature_name, data_frequency_plan_id, status, computation_frequency, unit)
SELECT 'VEHICLE_STATUS_UPDATE', id, 'ACTIVE', 10, 'SECONDS'
FROM data_frequency_plan
WHERE name = 'High Frequency'
UNION ALL
SELECT 'VEHICLE_STATUS_UPDATE', id, 'ACTIVE', 30, 'SECONDS'
FROM data_frequency_plan
WHERE name = 'Medium Frequency'
UNION ALL
SELECT 'VEHICLE_STATUS_UPDATE', id, 'ACTIVE', 60, 'SECONDS'
FROM data_frequency_plan
WHERE name = 'Low Frequency';


--data insertion association table into data_frequency_plan_details_aggregate_names
insert into data_frequency_plan_details_aggregate_names (data_frequency_plan_details_id,aggregate_name_id)
select dfpd.id,an.id
from data_frequency_plan_details dfpd, aggregate_name an
where dfpd.feature_name='VEHICLE_STATUS_UPDATE' and computation_frequency=10 and an.name in ('vehicle_status_telemetry_10sec_aggregate',
																					'vehicle_status_battery_10sec_aggregate')
UNION ALL
select dfpd.id,an.id
from data_frequency_plan_details dfpd, aggregate_name an
where dfpd.feature_name='VEHICLE_STATUS_UPDATE' and computation_frequency=30 and an.name in ('vehicle_status_telemetry_30sec_aggregate',
																					'vehicle_status_battery_30sec_aggregate')
UNION ALL
select dfpd.id,an.id
from data_frequency_plan_details dfpd, aggregate_name an
where dfpd.feature_name='VEHICLE_STATUS_UPDATE' and computation_frequency=60 and an.name in ('vehicle_status_telemetry_1min_aggregate',
																					'vehicle_status_battery_1min_aggregate');

--inserting data into cron_frequency
insert into cron_frequency (cron_time,unit)
values (0,'MINUTES');

-- data insertion into data_frequency_plan_details_cron_frequencies
INSERT INTO cron_frequency_data_frequency_plan_details (cron_frequency_id, data_frequency_plan_details_id)
SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'VEHICLE_STATUS_UPDATE'
  AND dfpd.computation_frequency = 10
  AND cf.cron_time IN (0)
  AND cf.unit = 'MINUTES'

UNION ALL

SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'VEHICLE_STATUS_UPDATE'
  AND dfpd.computation_frequency = 30
  AND cf.cron_time IN (0)
  AND cf.unit = 'MINUTES'

UNION ALL

SELECT cf.id, dfpd.id
FROM cron_frequency cf, data_frequency_plan_details dfpd
WHERE dfpd.feature_name = 'VEHICLE_STATUS_UPDATE'
  AND dfpd.computation_frequency = 60
  AND cf.cron_time IN (0)
  AND cf.unit = 'MINUTES';
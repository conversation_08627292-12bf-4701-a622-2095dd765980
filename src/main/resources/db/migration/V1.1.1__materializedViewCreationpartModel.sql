
CREATE MATERIALIZED VIEW evdata.view_vehicle_part_part_model AS
 SELECT v.imei,
    concat(vm.model_no, '.', p.mfr_org_id, '.', p.part_type, '.', p.part_model_id) AS vehicle_model_org_part_type_part_model_id
   FROM (((evdata.vehicle v
     JOIN evdata.vehicle_model vm ON ((vm.id = v.vehicle_model_id)))
     JOIN evdata.vehicle_parts vp ON ((vp.vehicle_id = v.id)))
     JOIN evdata.part p ON ((vp.part_id = p.id)));

-- create constraints
ALTER TABLE evdata.vehicle_motor_data_error
ADD CONSTRAINT vehicle_motor_data_error_pk PRIMARY KEY (timestamp, imei, motor_id);

ALTER TABLE evdata.vehicle_motor_data_error
ADD CONSTRAINT vehicle_motor_data_error_motor_fk FOREIGN KEY (motor_id)
REFERENCES evdata.part (id);

ALTER TABLE evdata.vehicle_motor_data_error
ADD CONSTRAINT vehicle_motor_data_error_mfr_org_fk FOREIGN KEY (mfr_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_motor_data_error
ADD CONSTRAINT vehicle_motor_data_error_owner_org_fk FOREIGN KEY (owner_org_id)
REFERENCES evusers.organisations (id);

ALTER TABLE evdata.vehicle_motor_data_error
ADD CONSTRAINT vehicle_motor_data_error_vehicle_fk FOREIGN KEY (vehicle_id)
REFERENCES evdata.vehicle (id);


CREATE INDEX vehicle_motor_data_error_id_idx ON evdata.vehicle_motor_data_error (motor_id, vehicle_id,imei, timestamp);


-- create hypertable for motor data
SELECT create_hypertable('vehicle_motor_data_error', 'timestamp', migrate_data => true);

-- set chunk time interval for motor hypertables
SELECT set_chunk_time_interval('vehicle_motor_data_error', INTERVAL '7 days');

--set retention policy for motor hypertables
SELECT add_retention_policy('vehicle_motor_data_error', INTERVAL '3 months');
-- enable compression for motor hypertables
ALTER TABLE vehicle_motor_data_error
SET (
	timescaledb.compress,
	timescaledb.compress_segmentby = 'imei,motor_id,vehicle_id,mfr_org_id,owner_org_id',
	timescaledb.compress_orderby='timestamp'
);

-- enable compression policies for motor hypertables
SELECT add_compression_policy('vehicle_motor_data_error', INTERVAL '1 days');

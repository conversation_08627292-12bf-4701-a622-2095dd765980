-- vehicle_status_motor_10sec_aggregate view creation
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_status_motor_10sec_aggregate
WITH (timescaledb.continuous) AS
select
vtd.imei as imei,
time_bucket ('10s',vtd.timestamp) as time_bucket,
min(created_on) as created_on,
mode() within group (order by vtd.motor_speed) as motor_speed
from vehicle_motor_data vtd
where vtd.timestamp > '2025-01-01'
group by vtd.imei,time_bucket('10s',vtd.timestamp) WITH NO DATA;


-- setting up the aggregate policy for vehicle_status_motor_10sec_aggregate
SELECT add_continuous_aggregate_policy('vehicle_status_motor_10sec_aggregate',
start_offset => INTERVAL '1 day',
end_offset => INTERVAL '10 seconds',
schedule_interval => INTERVAL '10 seconds');


-- vehicle_status_motor_30sec_aggregate view creation
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_status_motor_30sec_aggregate
WITH (timescaledb.continuous) AS
select
vtd.imei as imei,
time_bucket ('30s',vtd.time_bucket) as time_bucket,
min(created_on) as created_on,
mode() within group (order by vtd.motor_speed) as motor_speed
from vehicle_status_motor_10sec_aggregate vtd
where vtd.time_bucket > '2025-01-01'
group by vtd.imei,time_bucket('30s',vtd.time_bucket) WITH NO DATA;


-- setting up the aggregate policy for vehicle_status_motor_30sec_aggregate
SELECT add_continuous_aggregate_policy('vehicle_status_motor_30sec_aggregate',
start_offset => INTERVAL '1 day',
end_offset => INTERVAL '30 seconds',
schedule_interval => INTERVAL '30 seconds');

-- vehicle_status_telemetry_1min_aggregate view creation
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_status_motor_1min_aggregate
WITH (timescaledb.continuous) AS
select
vtd.imei as imei,
time_bucket ('1m',vtd.time_bucket) as time_bucket,
min (created_on) as created_on,
mode() within group (order by vtd.motor_speed) as motor_speed
from vehicle_status_motor_30sec_aggregate vtd
where vtd.time_bucket > '2025-01-01'
group by vtd.imei,time_bucket('1m',vtd.time_bucket) WITH NO DATA;


-- setting up the aggregate policy for vehicle_status_motor_1min_aggregate
SELECT add_continuous_aggregate_policy('vehicle_status_motor_1min_aggregate',
start_offset => INTERVAL '1 day',
end_offset => INTERVAL '1 minute',
schedule_interval => INTERVAL '1 minute');
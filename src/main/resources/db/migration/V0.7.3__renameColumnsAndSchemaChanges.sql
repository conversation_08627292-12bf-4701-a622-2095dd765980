ALTER TABLE vehicle_battery_data DROP COLUMN IF EXISTS owner_org;
ALTER TABLE vehicle_location_data DROP COLUMN IF EXISTS owner_org;
ALTER TABLE vehicle_telemetry_data DROP COLUMN IF EXISTS owner_org;
ALTER TABLE vehicle_telemetry_data DROP COLUMN IF EXISTS vehicle_model_id;
ALTER TABLE vehicle_telemetry_data_raw DROP COLUMN IF EXISTS vehicle_model_id;
ALTER TABLE battery_stack DROP COLUMN IF EXISTS voltage;
ALTER TABLE battery_stack_raw DROP COLUMN IF EXISTS voltage;
ALTER TABLE trip_details DROP COLUMN IF EXISTS drive_mode;
ALTER TABLE part DROP COLUMN IF EXISTS manufacturer_name;
ALTER TABLE vehicle_model DROP COLUMN IF EXISTS variant_id;
ALTER TABLE vehicle DROP COLUMN IF EXISTS birth_time;
DROP TABLE IF EXISTS charger_model;
DROP TABLE IF EXISTS chassis_model;
DROP TABLE IF EXISTS organisations_linked_organisation;
DROP TABLE IF EXISTS grafana_teams;
ALTER TABLE fleet RENAME COLUMN organisation_id to owner_org_id;
ALTER TABLE part RENAME COLUMN manufacturer_id to mfr_org_id;
ALTER TABLE battery_cell RENAME COLUMN owner_org to owner_org_id;
ALTER TABLE battery_cell_raw RENAME COLUMN owner_org to owner_org_id;
ALTER TABLE vehicle RENAME COLUMN manufacturer_id to mfr_org_id;
ALTER TABLE vehicle RENAME COLUMN owner_id to owner_org_id;
ALTER TABLE battery_stack_raw RENAME COLUMN owner_org to owner_org_id;
ALTER TABLE battery_stack RENAME COLUMN owner_org to owner_org_id;
ALTER TABLE part_model RENAME COLUMN manufacturer_id to mfr_org_id;





















DO $$
DECLARE
    start_date TIMESTAMP;
    end_date TIMESTAMP;
    current_start TIMESTAMP;
    current_end TIMESTAMP;
BEGIN
    -- Get date range
    SELECT MIN(timestamp), MAX(timestamp)
    INTO start_date, end_date
    FROM evdata.vehicle_telemetry_data_raw;

    -- Initialize batch range
    current_start := start_date;
    current_end := start_date + INTERVAL '3 days';

    -- Loop through date range in 3-day batches
    WHILE current_start <= end_date LOOP
        -- Insert batch
        INSERT INTO evdata.vehicle_imu_data_raw (
			accel_x_axis,
    		accel_y_axis,
    		accel_z_axis,
			grv_x_axis,
    		grv_y_axis,
    		grv_z_axis,
			gyro_x_axis,
    		gyro_y_axis,
    		gyro_z_axis,
    		timestamp,
            imei,
            vehicle_id,
            created_on,
            mfr_org_id,
            owner_org_id,
            packet_received_on,
            co_relation_id,
            di_ignition,
            di_motion,
            ai_lean_angle
        )
        SELECT
            vtd.accel_x_axis,
    		vtd.accel_y_axis,
    		vtd.accel_z_axis,
			vtd.grv_x_axis,
    		vtd.grv_y_axis,
    		vtd.grv_z_axis,
			vtd.gyro_x_axis,
    		vtd.gyro_y_axis,
    		vtd.gyro_z_axis,
    		vtd.timestamp,
            vtd.imei,
            vtd.vehicle_id,
            vtd.created_on,
            vtd.mfr_org_id,
            vtd.owner_org_id,
            vtd.packet_received_on,
            vtd.co_relation_id,
            di_ignition,
            di_motion,
            ai_lean_angle
        FROM
            evdata.vehicle_telemetry_data_raw vtd
        WHERE
            vtd.timestamp >= current_start
            AND vtd.timestamp < current_end;

        -- Move to next 3-day batch
        current_start := current_end;
        current_end := current_end + INTERVAL '3 days';

    END LOOP;
END $$;
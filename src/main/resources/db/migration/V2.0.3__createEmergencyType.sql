
-- Create sequence for emergency_type table
CREATE SEQUENCE IF NOT EXISTS emergency_type_id_seq START 1 INCREMENT 1;

-- Create emergency_type table
CREATE TABLE IF NOT EXISTS evdata.emergency_type (
    id BIGINT PRIMARY KEY DEFAULT nextval('emergency_type_id_seq'),
    organisation_id BIGINT NOT NULL,
    description VARCHAR(500) NOT NULL,
    CONSTRAINT fk_emergency_type_organisation_id
    FOREIGN KEY (organisation_id) REFERENCES evusers.organisations(id)
);


CREATE INDEX IF NOT EXISTS idx_emergency_type_organisation_id ON evdata.emergency_type(organisation_id);



-- Insert emergency types for 'NDS Eco Motors' organisation
INSERT INTO evdata.emergency_type (organisation_id, description) VALUES
((SELECT o.id FROM evusers.organisations o
  JOIN evusers.organisation_profiles op ON o.organisation_profile_id = op.id
  WHERE op.name = 'NDS Eco Motors'), 'Out of charge'),
((SELECT o.id FROM evusers.organisations o
  JOIN evusers.organisation_profiles op ON o.organisation_profile_id = op.id
  WHERE op.name = 'NDS Eco Motors'), 'Tow my bike'),
((SELECT o.id FROM evusers.organisations o
  JOIN evusers.organisation_profiles op ON o.organisation_profile_id = op.id
  WHERE op.name = 'NDS Eco Motors'), 'Flat tyre'),
((SELECT o.id FROM evusers.organisations o
  JOIN evusers.organisation_profiles op ON o.organisation_profile_id = op.id
  WHERE op.name = 'NDS Eco Motors'), 'Repair');
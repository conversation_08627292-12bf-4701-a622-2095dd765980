-- Create the materialized view for 10 sec aggregate
CREATE MATERIALIZED VIEW IF NOT EXISTS view_vehicle_battery_data_aggregate_per_10sec
WITH (timescaledb.continuous) AS
SELECT
    vbd.imei,
    vbd.vehicle_id,
    time_bucket('10s', vbd.timestamp) AS time_bucket,
    MAX(vbd.battery_volt) AS max_battery_volt,
    MIN(vbd.battery_volt) AS min_battery_volt,
    MAX(vbd.current) AS max_current,
    MIN(vbd.current) AS min_current,
    MAX(vbd.temperature_max) AS max_temperature_max,
    MIN(vbd.temperature_min) AS min_temperature_min
FROM
    vehicle_battery_data vbd
GROUP BY
    vbd.imei, vbd.vehicle_id, time_bucket('10s', vbd.timestamp)
WITH NO DATA;

-- Add continuous aggregate policy
SELECT add_continuous_aggregate_policy('view_vehicle_battery_data_aggregate_per_10sec',
 start_offset => INTERVAL '1 day',
 end_offset => INTERVAL '1 second',
 schedule_interval => INTERVAL '10 second');

-- Create the materialized view per min
CREATE MATERIALIZED VIEW IF NOT EXISTS view_vehicle_battery_data_aggregate_per_1min
WITH (timescaledb.continuous) AS
SELECT
    v1.imei,
    v1.vehicle_id,
    time_bucket('1 minute', v1.time_bucket) AS time_bucket_1min,
    MAX(v1.max_battery_volt) AS max_battery_volt,
    MIN(v1.min_battery_volt) AS min_battery_volt,
    MAX(v1.max_current) AS max_current,
    MIN(v1.min_current) AS min_current,
    MAX(v1.max_temperature_max) AS max_temperature_max,
    MIN(v1.min_temperature_min) AS min_temperature_min
FROM
    view_vehicle_battery_data_aggregate_per_10sec v1
GROUP BY
    v1.imei, v1.vehicle_id, time_bucket('1 minute', v1.time_bucket)
WITH NO DATA;

-- Add continuous aggregate policy for 1-minute view
SELECT add_continuous_aggregate_policy('view_vehicle_battery_data_aggregate_per_1min',
 start_offset => INTERVAL '1 day',
 end_offset => INTERVAL '1 second',
 schedule_interval => INTERVAL '1 minute');


-- Create the materialized vehicle_telemetry_data_availability_aggregate_daily
CREATE MATERIALIZED VIEW evdata.vehicle_telemetry_data_availability_aggregate_daily
WITH (timescaledb.continuous) AS
SELECT
    vehicle_id,
    imei,
    time_bucket('1 day', timestamp) AS day,
    COUNT(*) AS telemetry_data_availability
FROM
    evdata.vehicle_telemetry_data
GROUP BY
    vehicle_id,
    imei,
    day
WITH NO DATA;

CREATE INDEX telemetry_availability_idx_vehicle_id
ON evdata.vehicle_telemetry_data_availability_aggregate_daily (vehicle_id);

CREATE INDEX telemetry_availability_idx_day_imei_telemetry
ON evdata.vehicle_telemetry_data_availability_aggregate_daily (day, imei, telemetry_data_availability);

SELECT add_continuous_aggregate_policy('evdata.vehicle_telemetry_data_availability_aggregate_daily',
    start_offset => INTERVAL '2 day',
    end_offset => INTERVAL '0 day',
    schedule_interval => INTERVAL '5 minutes');





-- dropping the existing view
drop MATE<PERSON><PERSON>IZED VIEW IF EXISTS vehicle_status_telemetry_aggregate;
drop MATERIALIZED VIEW IF EXISTS vehicle_status_battery_aggregate;

-- vehicle_status_telemetry_10sec_aggregate view creation
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_status_telemetry_10sec_aggregate
WITH (timescaledb.continuous) AS
select
vtd.imei as imei,
time_bucket ('10s',vtd.timestamp) as time_bucket,
min(created_on) as created_on,
mode() within group (order by vtd.di_main_power) as di_main_power,
mode() within group (order by vtd.di_ignition) as di_ignition,
mode() within group (order by vtd.di_motion) as di_motion
from vehicle_telemetry_data vtd
where vtd.timestamp > '2024-01-01'
group by vtd.imei,time_bucket('10s',vtd.timestamp) WITH NO DATA;


-- setting up the aggregate policy for vehicle_status_telemetry_10sec_aggregate
SELECT add_continuous_aggregate_policy('vehicle_status_telemetry_10sec_aggregate',
start_offset => INTERVAL '1 day',
end_offset => INTERVAL '10 seconds',
schedule_interval => INTERVAL '10 seconds');

-- vehicle_status_telemetry_30sec_aggregate view creation
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_status_telemetry_30sec_aggregate
WITH (timescaledb.continuous) AS
select
vtd.imei as imei,
time_bucket ('30s',vtd.time_bucket) as time_bucket,
min(created_on) as created_on,
mode() within group (order by vtd.di_main_power) as di_main_power,
mode() within group (order by vtd.di_ignition) as di_ignition,
mode() within group (order by vtd.di_motion) as di_motion
from vehicle_status_telemetry_10sec_aggregate vtd
where vtd.time_bucket > '2024-01-01'
group by vtd.imei,time_bucket('30s',vtd.time_bucket) WITH NO DATA;


-- setting up the aggregate policy for vehicle_status_telemetry_30sec_aggregate
SELECT add_continuous_aggregate_policy('vehicle_status_telemetry_30sec_aggregate',
start_offset => INTERVAL '1 day',
end_offset => INTERVAL '30 seconds',
schedule_interval => INTERVAL '30 seconds');

-- vehicle_status_telemetry_1min_aggregate view creation
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_status_telemetry_1min_aggregate
WITH (timescaledb.continuous) AS
select
vtd.imei as imei,
time_bucket ('1m',vtd.time_bucket) as time_bucket,
min (created_on) as created_on,
mode() within group (order by vtd.di_main_power) as di_main_power,
mode() within group (order by vtd.di_ignition) as di_ignition,
mode() within group (order by vtd.di_motion) as di_motion
from vehicle_status_telemetry_30sec_aggregate vtd
where vtd.time_bucket > '2024-01-01'
group by vtd.imei,time_bucket('1m',vtd.time_bucket) WITH NO DATA;


-- setting up the aggregate policy for vehicle_status_telemetry_1min_aggregate
SELECT add_continuous_aggregate_policy('vehicle_status_telemetry_1min_aggregate',
start_offset => INTERVAL '1 day',
end_offset => INTERVAL '1 minute',
schedule_interval => INTERVAL '1 minute');

-- vehicle_status_battery_10sec_aggregate creation
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_status_battery_10sec_aggregate
WITH (timescaledb.continuous) AS
select
vbd.imei as imei,
time_bucket ('10s',vbd.timestamp) as time_bucket,
min (created_on) as created_on,
    CASE
        WHEN COUNT(CASE WHEN vbd.current > 0 THEN 1 END) > COUNT(CASE WHEN vbd.current < 0 THEN 1 END) THEN 1
        WHEN COUNT(CASE WHEN vbd.current > 0 THEN 1 END) < COUNT(CASE WHEN vbd.current < 0 THEN 1 END) THEN -1
		WHEN COUNT(vbd.current) = COUNT(*) THEN NULL
        ELSE 0
    END AS current_sign
from vehicle_battery_data vbd
where vbd.timestamp > '2024-01-01'
group by vbd.imei,time_bucket ('10s',vbd.timestamp) WITH NO DATA;

--vehicle_status_battery_10sec_aggregate aggregate policy
SELECT add_continuous_aggregate_policy('vehicle_status_battery_10sec_aggregate',
start_offset => INTERVAL '1 day',
end_offset => INTERVAL '10 seconds',
schedule_interval => INTERVAL '10 seconds');

-- vehicle_status_battery_30sec_aggregate creation
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_status_battery_30sec_aggregate
WITH (timescaledb.continuous) AS
select
vbd.imei as imei,
time_bucket ('30s',vbd.time_bucket) as time_bucket,
min (created_on) as created_on,
    CASE
        WHEN COUNT(CASE WHEN vbd.current_sign > 0 THEN 1 END) > COUNT(CASE WHEN vbd.current_sign < 0 THEN 1 END) THEN 1
        WHEN COUNT(CASE WHEN vbd.current_sign > 0 THEN 1 END) < COUNT(CASE WHEN vbd.current_sign < 0 THEN 1 END) THEN -1
		WHEN COUNT(vbd.current_sign) = COUNT(*) THEN NULL
        ELSE 0
    END AS current_sign
from vehicle_status_battery_10sec_aggregate vbd
where vbd.time_bucket > '2024-01-01'
group by vbd.imei,time_bucket ('30s',vbd.time_bucket) WITH NO DATA;

--vehicle_status_battery_30sec_aggregate aggregate policy
SELECT add_continuous_aggregate_policy('vehicle_status_battery_30sec_aggregate',
start_offset => INTERVAL '1 day',
end_offset => INTERVAL '30 seconds',
schedule_interval => INTERVAL '30 seconds');

-- vehicle_status_battery_1min_aggregate creation
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_status_battery_1min_aggregate
WITH (timescaledb.continuous) AS
select
vbd.imei as imei,
time_bucket ('1m',vbd.time_bucket) as time_bucket,
min(created_on) as created_on,
    CASE
        WHEN COUNT(CASE WHEN vbd.current_sign > 0 THEN 1 END) > COUNT(CASE WHEN vbd.current_sign < 0 THEN 1 END) THEN 1
        WHEN COUNT(CASE WHEN vbd.current_sign > 0 THEN 1 END) < COUNT(CASE WHEN vbd.current_sign < 0 THEN 1 END) THEN -1
		WHEN COUNT(vbd.current_sign) = COUNT(*) THEN NULL
        ELSE 0
    END AS current_sign
from vehicle_status_battery_30sec_aggregate vbd
where vbd.time_bucket > '2024-01-01'
group by vbd.imei,time_bucket ('1m',vbd.time_bucket) WITH NO DATA;

--vehicle_status_battery_1min_aggregate aggregate policy
SELECT add_continuous_aggregate_policy('vehicle_status_battery_1min_aggregate',
start_offset => INTERVAL '1 day',
end_offset => INTERVAL '1 minute',
schedule_interval => INTERVAL '1 minute');
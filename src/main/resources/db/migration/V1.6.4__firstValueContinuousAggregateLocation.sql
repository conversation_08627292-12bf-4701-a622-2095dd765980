--Continuous Aggregate for vehicle_location_data across 1 minute window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_location_first_aggregate_1m
WITH (timescaledb.continuous) AS
SELECT
    vld.vehicle_id AS vehicle_id,
    vld.imei AS imei,
    vld.mfr_org_id AS mfr_org_id,
    vld.owner_org_id AS owner_org_id,
    time_bucket('1m', vld.timestamp) AS bucket,
    first(vld.altitude, vld.timestamp) FILTER (WHERE vld.altitude IS NOT NULL) AS altitude,
    first(vld.brg, vld.timestamp) FILTER (WHERE vld.brg IS NOT NULL) AS brg,
    first(vld.hdop, vld.timestamp) FILTER (WHERE vld.hdop IS NOT NULL) AS hdop,
    first(vld.latitude, vld.timestamp) FILTER (WHERE vld.latitude IS NOT NULL) AS latitude,
    first(vld.longitude, vld.timestamp) FILTER (WHERE vld.longitude IS NOT NULL) AS longitude,
    first(vld.pdop, vld.timestamp) FILTER (WHERE vld.pdop IS NOT NULL) AS pdop,
    first(vld.speed, vld.timestamp) FILTER (WHERE vld.speed IS NOT NULL) AS speed,
    first(vld.track_sats, vld.timestamp) FILTER (WHERE vld.track_sats IS NOT NULL) AS track_sats,
    first(vld.vdop, vld.timestamp) FILTER (WHERE vld.vdop IS NOT NULL) AS vdop,
    first(vld.view_sats, vld.timestamp) FILTER (WHERE vld.view_sats IS NOT NULL) AS view_sats,
    first(vld.geo_point_zm, vld.timestamp) FILTER (WHERE vld.geo_point_zm IS NOT NULL) AS geo_point_zm
FROM vehicle_location_data vld
GROUP BY bucket, vld.vehicle_id, vld.imei, vld.mfr_org_id, vld.owner_org_id
WITH NO DATA;

--Refresh the vehicle_location_first_aggregate_1m every minute
SELECT add_continuous_aggregate_policy('vehicle_location_first_aggregate_1m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');



--Continuous Aggregate for vehicle_location_data across 10 minutes window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_location_first_aggregate_10m
WITH (timescaledb.continuous) AS
SELECT
    vlf1m.vehicle_id AS vehicle_id,
    vlf1m.imei AS imei,
    vlf1m.mfr_org_id AS mfr_org_id,
    vlf1m.owner_org_id AS owner_org_id,
    time_bucket('10m', vlf1m.bucket) AS bucket,
    first(vlf1m.altitude, vlf1m.bucket) FILTER (WHERE vlf1m.altitude IS NOT NULL) AS altitude,
    first(vlf1m.brg, vlf1m.bucket) FILTER (WHERE vlf1m.brg IS NOT NULL) AS brg,
    first(vlf1m.hdop, vlf1m.bucket) FILTER (WHERE vlf1m.hdop IS NOT NULL) AS hdop,
    first(vlf1m.latitude, vlf1m.bucket) FILTER (WHERE vlf1m.latitude IS NOT NULL) AS latitude,
    first(vlf1m.longitude, vlf1m.bucket) FILTER (WHERE vlf1m.longitude IS NOT NULL) AS longitude,
    first(vlf1m.pdop, vlf1m.bucket) FILTER (WHERE vlf1m.pdop IS NOT NULL) AS pdop,
    first(vlf1m.speed, vlf1m.bucket) FILTER (WHERE vlf1m.speed IS NOT NULL) AS speed,
    first(vlf1m.track_sats, vlf1m.bucket) FILTER (WHERE vlf1m.track_sats IS NOT NULL) AS track_sats,
    first(vlf1m.vdop, vlf1m.bucket) FILTER (WHERE vlf1m.vdop IS NOT NULL) AS vdop,
    first(vlf1m.view_sats, vlf1m.bucket) FILTER (WHERE vlf1m.view_sats IS NOT NULL) AS view_sats,
    first(vlf1m.geo_point_zm, vlf1m.bucket) FILTER (WHERE vlf1m.geo_point_zm IS NOT NULL) AS geo_point_zm
FROM vehicle_location_first_aggregate_1m vlf1m
GROUP BY time_bucket('10m', vlf1m.bucket), vlf1m.vehicle_id, vlf1m.imei, vlf1m.mfr_org_id, vlf1m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_location_first_aggregate_1m every 10 minutes
SELECT add_continuous_aggregate_policy('vehicle_location_first_aggregate_10m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '10 minute');



--Continuous Aggregate for vehicle_location_data across 1 hour window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_location_first_aggregate_1h
WITH (timescaledb.continuous) AS
SELECT
    vlf10m.vehicle_id AS vehicle_id,
    vlf10m.imei AS imei,
    vlf10m.mfr_org_id AS mfr_org_id,
    vlf10m.owner_org_id AS owner_org_id,
    time_bucket('1h', vlf10m.bucket) AS bucket,
    first(vlf10m.altitude, vlf10m.bucket) FILTER (WHERE vlf10m.altitude IS NOT NULL) AS altitude,
    first(vlf10m.brg, vlf10m.bucket) FILTER (WHERE vlf10m.brg IS NOT NULL) AS brg,
    first(vlf10m.hdop, vlf10m.bucket) FILTER (WHERE vlf10m.hdop IS NOT NULL) AS hdop,
    first(vlf10m.latitude, vlf10m.bucket) FILTER (WHERE vlf10m.latitude IS NOT NULL) AS latitude,
    first(vlf10m.longitude, vlf10m.bucket) FILTER (WHERE vlf10m.longitude IS NOT NULL) AS longitude,
    first(vlf10m.pdop, vlf10m.bucket) FILTER (WHERE vlf10m.pdop IS NOT NULL) AS pdop,
    first(vlf10m.speed, vlf10m.bucket) FILTER (WHERE vlf10m.speed IS NOT NULL) AS speed,
    first(vlf10m.track_sats, vlf10m.bucket) FILTER (WHERE vlf10m.track_sats IS NOT NULL) AS track_sats,
    first(vlf10m.vdop, vlf10m.bucket) FILTER (WHERE vlf10m.vdop IS NOT NULL) AS vdop,
    first(vlf10m.view_sats, vlf10m.bucket) FILTER (WHERE vlf10m.view_sats IS NOT NULL) AS view_sats,
    first(vlf10m.geo_point_zm, vlf10m.bucket) FILTER (WHERE vlf10m.geo_point_zm IS NOT NULL) AS geo_point_zm
FROM vehicle_location_first_aggregate_10m vlf10m
GROUP BY time_bucket('1h', vlf10m.bucket), vlf10m.vehicle_id, vlf10m.imei, vlf10m.mfr_org_id, vlf10m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_location_first_aggregate_1h every hour
SELECT add_continuous_aggregate_policy('vehicle_location_first_aggregate_1h',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 hour');




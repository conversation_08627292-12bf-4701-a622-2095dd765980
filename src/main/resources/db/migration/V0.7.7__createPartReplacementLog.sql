CREATE SEQUENCE IF NOT EXISTS part_replacement_log_seq START WITH 1 INCREMENT BY 50;

CREATE TABLE part_replacement_log (
    id BIGINT NOT NULL,
    vehicle_id BIGINT,
    part_id BIGINT,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    imei VARCHAR,
    part_type VARCHAR,
    modified_by B<PERSON>IN<PERSON>,
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS part_replacement_log_veh_idx ON part_replacement_log (vehicle_id);
CREATE INDEX IF NOT EXISTS part_replacement_log_part_type_idx ON part_replacement_log (part_type);
CREATE INDEX IF NOT EXISTS part_replacement_log_imei_idx ON part_replacement_log (imei);

ALTER TABLE IF EXISTS part_replacement_log
ADD CONSTRAINT fk_vehicle_id FOREIGN KEY (vehicle_id) REFERENCES vehicle,
ADD CONSTRAINT fk_part_id FOREIGN KEY (part_id) REFERENCES part,
ADD CONSTRAINT fk_modified_by <PERSON><PERSON><PERSON><PERSON><PERSON>EY (modified_by) REFERENCES users,
ADD CONSTRAINT unique_vehicle_part_start UNIQUE (vehicle_id, part_type, start_time);
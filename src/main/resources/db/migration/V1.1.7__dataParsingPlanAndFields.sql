CREATE SEQUENCE IF NOT EXISTS data_parsing_field_seq start with 1 increment by 1;
CREATE TABLE IF NOT EXISTS data_parsing_field(
	id bigint PRIMARY KEY DEFAULT nextval('data_parsing_field_seq'),
	field_name <PERSON><PERSON>HA<PERSON>(255),
	field_type character varying(255),
    CONSTRAINT field_type_check CHECK (field_type::text = ANY (ARRAY['TELEMETRY'::character varying::text, 'BATTERY'::character varying::text,
																	   'LOCATION'::character varying::text,'BATTERY_STACK'::character varying::text,
																	   'BATTERY_CELL'::character varying::text,'BATTERY_ALARM'::character varying::text]))
);

CREATE SEQUENCE IF NOT EXISTS data_parsing_plan_seq start with 1 increment by 1;
CREATE TABLE IF NOT EXISTS data_parsing_plan (
	id bigint PRIMARY KEY DEFAULT nextval('data_parsing_plan_seq'),
	name <PERSON><PERSON><PERSON><PERSON>(255),
	description character varying(255),
	plan_status character varying(255),
	CONSTRAINT plan_status_check CHECK (plan_status::text = ANY (ARRAY['ACTIVE'::character varying::text, 'INACTIVE'::character varying::text]))
);

CREATE TABLE IF NOT EXISTS data_parsing_plan_fields(
	data_parsing_plan_id bigint,
	field_id bigint,
	FOREIGN KEY (data_parsing_plan_id) REFERENCES data_parsing_plan(id),
	FOREIGN KEY (field_id) REFERENCES data_parsing_field(id)
);

--adding data_parsing_plan_id column to active_vehicle_subscription_plan
ALTER TABLE active_vehicle_subscription_plan
ADD COLUMN data_parsing_plan_id BIGINT;
ALTER TABLE active_vehicle_subscription_plan
ADD CONSTRAINT fk_data_parsing_plan_id
FOREIGN KEY (data_parsing_plan_id)
REFERENCES data_parsing_plan(id);

--adding data_parsing_plan_id column to combo_plan
ALTER TABLE combo_plan
ADD COLUMN data_parsing_plan_id BIGINT;
ALTER TABLE combo_plan
ADD CONSTRAINT fk_data_parsing_plan_id
FOREIGN KEY (data_parsing_plan_id)
REFERENCES data_parsing_plan(id);

--data insertion in to data_parsing_field
INSERT INTO data_parsing_field (field_name, field_type)
VALUES
('accel_x_axis', 'TELEMETRY'),
('accel_y_axis', 'TELEMETRY'),
('accel_z_axis', 'TELEMETRY'),
('ai_lean_angle', 'TELEMETRY'),
('ai_system_voltage', 'TELEMETRY'),
('ai_temperature', 'TELEMETRY'),
('ai_vbuck', 'TELEMETRY'),
('ai_voltage_input', 'TELEMETRY'),
('ai_vusr1', 'TELEMETRY'),
('ai_vusr2', 'TELEMETRY'),
('co_relation_id', 'TELEMETRY'),
('di_ignition', 'TELEMETRY'),
('di_main_power', 'TELEMETRY'),
('di_motion', 'TELEMETRY'),
('di_tamper', 'TELEMETRY'),
('di_usr1', 'TELEMETRY'),
('di_usr2', 'TELEMETRY'),
('do_usr1', 'TELEMETRY'),
('do_usr2', 'TELEMETRY'),
('gyro_x_axis', 'TELEMETRY'),
('gyro_y_axis', 'TELEMETRY'),
('gyro_z_axis', 'TELEMETRY'),
('motor_brake', 'TELEMETRY'),
('motor_cruise', 'TELEMETRY'),
('motor_dc_current', 'TELEMETRY'),
('motor_dc_voltage', 'TELEMETRY'),
('motor_driving_mode', 'TELEMETRY'),
('motor_fault_feedback', 'TELEMETRY'),
('motor_mcs_temperature', 'TELEMETRY'),
('motor_parking_sign', 'TELEMETRY'),
('motor_ready_sign', 'TELEMETRY'),
('motor_regeneration', 'TELEMETRY'),
('motor_reverse', 'TELEMETRY'),
('motor_side_stand', 'TELEMETRY'),
('motor_speed', 'TELEMETRY'),
('motor_temperature', 'TELEMETRY'),
('motor_throttle', 'TELEMETRY'),
('grv_x_axis', 'TELEMETRY'),
('grv_y_axis', 'TELEMETRY'),
('grv_z_axis', 'TELEMETRY'),
('battery_volt', 'BATTERY'),
('cell_volt_max', 'BATTERY'),
('cell_volt_min', 'BATTERY'),
('chg_cycle_count', 'BATTERY'),
('co_relation_id', 'BATTERY'),
('current', 'BATTERY'),
('dsg_cycle_count', 'BATTERY'),
('packet_received_on', 'BATTERY'),
('soc', 'BATTERY'),
('soh', 'BATTERY'),
('temperature_max', 'BATTERY'),
('temperature_min', 'BATTERY'),
('remaining_capacity', 'BATTERY'),
('mosfet_temperature', 'BATTERY'),
('altitude', 'LOCATION'),
('brg', 'LOCATION'),
('co_relation_id', 'LOCATION'),
('hdop', 'LOCATION'),
('latitude', 'LOCATION'),
('longitude', 'LOCATION'),
('packet_received_on', 'LOCATION'),
('pdop', 'LOCATION'),
('speed', 'LOCATION'),
('track_sats', 'LOCATION'),
('vdop', 'LOCATION'),
('view_sats', 'LOCATION'),
('geo_point_zm', 'LOCATION'),
('temperature', 'BATTERY_STACK'),
('co_relation_id', 'BATTERY_STACK'),
('balancing_status', 'BATTERY_CELL'),
('cell_voltage', 'BATTERY_CELL'),
('co_relation_id', 'BATTERY_CELL'),
('alarm_pin_set', 'BATTERY_ALARM'),
('protection_pin_set', 'BATTERY_ALARM'),
('part_type', 'BATTERY_ALARM');


--inserting data into data_parsing_plan
insert into data_parsing_plan (name,description,plan_status)
values
('Gold','Gold Package','ACTIVE'),
('Platinum','Platinum Package','ACTIVE'),
('Daimond','Daimond Package','ACTIVE');

--inserting data into combo_plan table
INSERT INTO combo_plan (data_parsing_plan_id, data_frequency_plan_id,name,status)
SELECT
    dpp.id AS data_parsing_plan_id,
    dfp.id AS data_frequency_plan_id,
    dpp.name || ' package + ' || dfp.name,
	'ACTIVE'
FROM
    data_parsing_plan dpp
CROSS JOIN
    data_frequency_plan dfp;

 --updating the rows active active_vehicle_subscription_plan with combo_plan and data_parsing_plan
 update active_vehicle_subscription_plan
 set combo_plan_id=cp.id, data_parsing_plan_id=dpp.id
 FROM data_parsing_plan dpp
 JOIN combo_plan cp ON cp.name = 'Daimond package + High Frequency'
 WHERE dpp.name = 'Daimond';

 --inserting data into data_parsing_plan_fields
 INSERT INTO data_parsing_plan_fields (data_parsing_plan_id, field_id)
 SELECT dpp.id, dpf.id
 FROM data_parsing_plan dpp, data_parsing_field dpf
 WHERE dpp.name = 'Gold' AND dpf.field_name IN (
     'ai_lean_angle',
     'ai_system_voltage',
     'ai_temperature',
     'di_ignition',
     'di_main_power',
     'motor_dc_current',
     'motor_dc_voltage',
     'motor_fault_feedback',
     'motor_mcs_temperature',
     'motor_speed',
     'motor_temperature',
     'motor_throttle',
     'vehicle_battery_data_volt',
     'current',
     'soc',
     'soh',
     'temperature_max',
     'temperature_min',
     'remaining_capacity',
     'mosfet_temperature',
     'altitude',
     'latitude',
     'longitude',
     'speed',
     'temperature',
     'balancing_status',
     'alarm_pin_set',
     'protection_pin_set',
     'part_type'
 );
INSERT INTO data_parsing_plan_fields (data_parsing_plan_id, field_id)
SELECT dpp.id, dpf.id
FROM data_parsing_plan dpp, data_parsing_field dpf
WHERE dpp.name = 'Platinum' AND dpf.field_name IN (
    'accel_x_axis',
    'accel_y_axis',
    'accel_z_axis',
    'ai_lean_angle',
    'ai_system_voltage',
    'di_ignition',
    'di_main_power',
    'motor_brake',
    'motor_cruise',
    'motor_dc_current',
    'motor_dc_voltage',
    'motor_fault_feedback',
    'motor_mcs_temperature',
    'motor_speed',
    'motor_temperature',
    'motor_throttle',
    'vehicle_battery_data_volt',
    'cell_volt_max',
    'cell_volt_min',
    'current',
    'soc',
    'soh',
    'temperature_max',
    'temperature_min',
    'remaining_capacity',
    'mosfet_temperature',
    'altitude',
    'brg',
    'latitude',
    'longitude',
    'speed',
    'temperature',
    'balancing_status',
    'alarm_pin_set',
    'protection_pin_set',
    'part_type'
);
INSERT INTO data_parsing_plan_fields (data_parsing_plan_id, field_id)
SELECT dpp.id, dpf.id
FROM data_parsing_plan dpp, data_parsing_field dpf
WHERE dpp.name = 'Daimond' AND dpf.field_name IN (
    'accel_x_axis',
    'accel_y_axis',
    'accel_z_axis',
    'ai_lean_angle',
    'ai_system_voltage',
    'ai_vbuck',
    'ai_voltage_input',
    'ai_vusr1',
    'ai_vusr2',
    'di_ignition',
    'di_main_power',
    'di_motion',
    'di_tamper',
    'di_usr1',
    'di_usr2',
    'do_usr1',
    'do_usr2',
    'gyro_x_axis',
    'gyro_y_axis',
    'gyro_z_axis',
    'motor_brake',
    'motor_cruise',
    'motor_dc_current',
    'motor_dc_voltage',
    'motor_driving_mode',
    'motor_fault_feedback',
    'motor_mcs_temperature',
    'motor_parking_sign',
    'motor_ready_sign',
    'motor_regeneration',
    'motor_reverse',
    'motor_side_stand',
    'motor_speed',
    'motor_temperature',
    'motor_throttle',
    'grv_x_axis',
    'grv_y_axis',
    'grv_z_axis',
    'vehicle_battery_data_volt',
    'cell_volt_max',
    'cell_volt_min',
    'chg_cycle_count',
    'current',
    'dsg_cycle_count',
    'soc',
    'soh',
    'temperature_max',
    'temperature_min',
    'remaining_capacity',
    'mosfet_temperature',
    'altitude',
    'brg',
    'latitude',
    'longitude',
    'speed',
    'temperature',
    'balancing_status',
    'alarm_pin_set',
    'protection_pin_set',
    'part_type'
);



--Continuous Aggregate for vehicle_telemetry_data across 1 minute window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_telemetry_first_aggregate_1m
WITH (timescaledb.continuous) AS
SELECT
    vtd.vehicle_id AS vehicle_id,
    vtd.imei AS imei,
    vtd.mfr_org_id AS mfr_org_id,
    vtd.owner_org_id AS owner_org_id,
    time_bucket('1m', vtd.timestamp) AS bucket,
    first(vtd.accel_x_axis, vtd.timestamp) FILTER (WHERE vtd.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vtd.accel_y_axis, vtd.timestamp) FILTER (WHERE vtd.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vtd.accel_z_axis, vtd.timestamp) FILTER (WHERE vtd.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vtd.ai_lean_angle, vtd.timestamp) FILTER (WHERE vtd.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vtd.ai_system_voltage, vtd.timestamp) FILTER (WHERE vtd.ai_system_voltage IS NOT NULL) AS ai_system_voltage,
    first(vtd.ai_temperature, vtd.timestamp) FILTER (WHERE vtd.ai_temperature IS NOT NULL) AS ai_temperature,
    first(vtd.ai_vbuck, vtd.timestamp) FILTER (WHERE vtd.ai_vbuck IS NOT NULL) AS ai_vbuck,
    first(vtd.ai_voltage_input, vtd.timestamp) FILTER (WHERE vtd.ai_voltage_input IS NOT NULL) AS ai_voltage_input,
    first(vtd.ai_vusr1, vtd.timestamp) FILTER (WHERE vtd.ai_vusr1 IS NOT NULL) AS ai_vusr1,
    first(vtd.ai_vusr2, vtd.timestamp) FILTER (WHERE vtd.ai_vusr2 IS NOT NULL) AS ai_vusr2,
    first(vtd.di_ignition, vtd.timestamp) FILTER (WHERE vtd.di_ignition IS NOT NULL) AS di_ignition,
    first(vtd.di_main_power, vtd.timestamp) FILTER (WHERE vtd.di_main_power IS NOT NULL) AS di_main_power,
    first(vtd.di_motion, vtd.timestamp) FILTER (WHERE vtd.di_motion IS NOT NULL) AS di_motion,
    first(vtd.di_tamper, vtd.timestamp) FILTER (WHERE vtd.di_tamper IS NOT NULL) AS di_tamper,
    first(vtd.di_usr1, vtd.timestamp) FILTER (WHERE vtd.di_usr1 IS NOT NULL) AS di_usr1,
    first(vtd.di_usr2, vtd.timestamp) FILTER (WHERE vtd.di_usr2 IS NOT NULL) AS di_usr2,
    first(vtd.do_usr1, vtd.timestamp) FILTER (WHERE vtd.do_usr1 IS NOT NULL) AS do_usr1,
    first(vtd.do_usr2, vtd.timestamp) FILTER (WHERE vtd.do_usr2 IS NOT NULL) AS do_usr2,
    first(vtd.gyro_x_axis, vtd.timestamp) FILTER (WHERE vtd.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vtd.gyro_y_axis, vtd.timestamp) FILTER (WHERE vtd.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vtd.gyro_z_axis, vtd.timestamp) FILTER (WHERE vtd.gyro_z_axis IS NOT NULL) AS gyro_z_axis,
    first(vtd.grv_x_axis, vtd.timestamp) FILTER (WHERE vtd.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vtd.grv_y_axis, vtd.timestamp) FILTER (WHERE vtd.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vtd.grv_z_axis, vtd.timestamp) FILTER (WHERE vtd.grv_z_axis IS NOT NULL) AS grv_z_axis
FROM vehicle_telemetry_data vtd
GROUP BY bucket, vtd.vehicle_id, vtd.imei, vtd.mfr_org_id, vtd.owner_org_id
WITH NO DATA;

--Refresh the vehicle_telemetry_first_aggregate_1m every minute
SELECT add_continuous_aggregate_policy('vehicle_telemetry_first_aggregate_1m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute');


--Continuous Aggregate for vehicle_telemetry_data across 10 minutes window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_telemetry_first_aggregate_10m
WITH (timescaledb.continuous) AS
SELECT
    vtf1m.vehicle_id AS vehicle_id,
    vtf1m.imei AS imei,
    vtf1m.mfr_org_id AS mfr_org_id,
    vtf1m.owner_org_id AS owner_org_id,
    time_bucket('10m', vtf1m.bucket) AS bucket,
    first(vtf1m.accel_x_axis, vtf1m.bucket) FILTER (WHERE vtf1m.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vtf1m.accel_y_axis, vtf1m.bucket) FILTER (WHERE vtf1m.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vtf1m.accel_z_axis, vtf1m.bucket) FILTER (WHERE vtf1m.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vtf1m.ai_lean_angle, vtf1m.bucket) FILTER (WHERE vtf1m.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vtf1m.ai_system_voltage, vtf1m.bucket) FILTER (WHERE vtf1m.ai_system_voltage IS NOT NULL) AS ai_system_voltage,
    first(vtf1m.ai_temperature, vtf1m.bucket) FILTER (WHERE vtf1m.ai_temperature IS NOT NULL) AS ai_temperature,
    first(vtf1m.ai_vbuck, vtf1m.bucket) FILTER (WHERE vtf1m.ai_vbuck IS NOT NULL) AS ai_vbuck,
    first(vtf1m.ai_voltage_input, vtf1m.bucket) FILTER (WHERE vtf1m.ai_voltage_input IS NOT NULL) AS ai_voltage_input,
    first(vtf1m.ai_vusr1, vtf1m.bucket) FILTER (WHERE vtf1m.ai_vusr1 IS NOT NULL) AS ai_vusr1,
    first(vtf1m.ai_vusr2, vtf1m.bucket) FILTER (WHERE vtf1m.ai_vusr2 IS NOT NULL) AS ai_vusr2,
    first(vtf1m.di_ignition, vtf1m.bucket) FILTER (WHERE vtf1m.di_ignition IS NOT NULL) AS di_ignition,
    first(vtf1m.di_main_power, vtf1m.bucket) FILTER (WHERE vtf1m.di_main_power IS NOT NULL) AS di_main_power,
    first(vtf1m.di_motion, vtf1m.bucket) FILTER (WHERE vtf1m.di_motion IS NOT NULL) AS di_motion,
    first(vtf1m.di_tamper, vtf1m.bucket) FILTER (WHERE vtf1m.di_tamper IS NOT NULL) AS di_tamper,
    first(vtf1m.di_usr1, vtf1m.bucket) FILTER (WHERE vtf1m.di_usr1 IS NOT NULL) AS di_usr1,
    first(vtf1m.di_usr2, vtf1m.bucket) FILTER (WHERE vtf1m.di_usr2 IS NOT NULL) AS di_usr2,
    first(vtf1m.do_usr1, vtf1m.bucket) FILTER (WHERE vtf1m.do_usr1 IS NOT NULL) AS do_usr1,
    first(vtf1m.do_usr2, vtf1m.bucket) FILTER (WHERE vtf1m.do_usr2 IS NOT NULL) AS do_usr2,
    first(vtf1m.gyro_x_axis, vtf1m.bucket) FILTER (WHERE vtf1m.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vtf1m.gyro_y_axis, vtf1m.bucket) FILTER (WHERE vtf1m.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vtf1m.gyro_z_axis, vtf1m.bucket) FILTER (WHERE vtf1m.gyro_z_axis IS NOT NULL) AS gyro_z_axis,
    first(vtf1m.grv_x_axis, vtf1m.bucket) FILTER (WHERE vtf1m.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vtf1m.grv_y_axis, vtf1m.bucket) FILTER (WHERE vtf1m.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vtf1m.grv_z_axis, vtf1m.bucket) FILTER (WHERE vtf1m.grv_z_axis IS NOT NULL) AS grv_z_axis
FROM vehicle_telemetry_first_aggregate_1m vtf1m
GROUP BY time_bucket('10m', vtf1m.bucket), vtf1m.vehicle_id, vtf1m.imei, vtf1m.mfr_org_id, vtf1m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_telemetry_first_aggregate_1m every 10 minutes
SELECT add_continuous_aggregate_policy('vehicle_telemetry_first_aggregate_10m',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '10 minute');


--Continuous Aggregate for vehicle_telemetry_data across 1 hour window with first non null value
CREATE MATERIALIZED VIEW IF NOT EXISTS vehicle_telemetry_first_aggregate_1h
WITH (timescaledb.continuous) AS
SELECT
    vtf10m.vehicle_id AS vehicle_id,
    vtf10m.imei AS imei,
    vtf10m.mfr_org_id AS mfr_org_id,
    vtf10m.owner_org_id AS owner_org_id,
    time_bucket('1h', vtf10m.bucket) AS bucket,
    first(vtf10m.accel_x_axis, vtf10m.bucket) FILTER (WHERE vtf10m.accel_x_axis IS NOT NULL) AS accel_x_axis,
    first(vtf10m.accel_y_axis, vtf10m.bucket) FILTER (WHERE vtf10m.accel_y_axis IS NOT NULL) AS accel_y_axis,
    first(vtf10m.accel_z_axis, vtf10m.bucket) FILTER (WHERE vtf10m.accel_z_axis IS NOT NULL) AS accel_z_axis,
    first(vtf10m.ai_lean_angle, vtf10m.bucket) FILTER (WHERE vtf10m.ai_lean_angle IS NOT NULL) AS ai_lean_angle,
    first(vtf10m.ai_system_voltage, vtf10m.bucket) FILTER (WHERE vtf10m.ai_system_voltage IS NOT NULL) AS ai_system_voltage,
    first(vtf10m.ai_temperature, vtf10m.bucket) FILTER (WHERE vtf10m.ai_temperature IS NOT NULL) AS ai_temperature,
    first(vtf10m.ai_vbuck, vtf10m.bucket) FILTER (WHERE vtf10m.ai_vbuck IS NOT NULL) AS ai_vbuck,
    first(vtf10m.ai_voltage_input, vtf10m.bucket) FILTER (WHERE vtf10m.ai_voltage_input IS NOT NULL) AS ai_voltage_input,
    first(vtf10m.ai_vusr1, vtf10m.bucket) FILTER (WHERE vtf10m.ai_vusr1 IS NOT NULL) AS ai_vusr1,
    first(vtf10m.ai_vusr2, vtf10m.bucket) FILTER (WHERE vtf10m.ai_vusr2 IS NOT NULL) AS ai_vusr2,
    first(vtf10m.di_ignition, vtf10m.bucket) FILTER (WHERE vtf10m.di_ignition IS NOT NULL) AS di_ignition,
    first(vtf10m.di_main_power, vtf10m.bucket) FILTER (WHERE vtf10m.di_main_power IS NOT NULL) AS di_main_power,
    first(vtf10m.di_motion, vtf10m.bucket) FILTER (WHERE vtf10m.di_motion IS NOT NULL) AS di_motion,
    first(vtf10m.di_tamper, vtf10m.bucket) FILTER (WHERE vtf10m.di_tamper IS NOT NULL) AS di_tamper,
    first(vtf10m.di_usr1, vtf10m.bucket) FILTER (WHERE vtf10m.di_usr1 IS NOT NULL) AS di_usr1,
    first(vtf10m.di_usr2, vtf10m.bucket) FILTER (WHERE vtf10m.di_usr2 IS NOT NULL) AS di_usr2,
    first(vtf10m.do_usr1, vtf10m.bucket) FILTER (WHERE vtf10m.do_usr1 IS NOT NULL) AS do_usr1,
    first(vtf10m.do_usr2, vtf10m.bucket) FILTER (WHERE vtf10m.do_usr2 IS NOT NULL) AS do_usr2,
    first(vtf10m.gyro_x_axis, vtf10m.bucket) FILTER (WHERE vtf10m.gyro_x_axis IS NOT NULL) AS gyro_x_axis,
    first(vtf10m.gyro_y_axis, vtf10m.bucket) FILTER (WHERE vtf10m.gyro_y_axis IS NOT NULL) AS gyro_y_axis,
    first(vtf10m.gyro_z_axis, vtf10m.bucket) FILTER (WHERE vtf10m.gyro_z_axis IS NOT NULL) AS gyro_z_axis,
    first(vtf10m.grv_x_axis, vtf10m.bucket) FILTER (WHERE vtf10m.grv_x_axis IS NOT NULL) AS grv_x_axis,
    first(vtf10m.grv_y_axis, vtf10m.bucket) FILTER (WHERE vtf10m.grv_y_axis IS NOT NULL) AS grv_y_axis,
    first(vtf10m.grv_z_axis, vtf10m.bucket) FILTER (WHERE vtf10m.grv_z_axis IS NOT NULL) AS grv_z_axis
FROM vehicle_telemetry_first_aggregate_10m vtf10m
GROUP BY time_bucket('1h', vtf10m.bucket), vtf10m.vehicle_id, vtf10m.imei, vtf10m.mfr_org_id, vtf10m.owner_org_id
WITH NO DATA;

--Refresh the vehicle_telemetry_first_aggregate_1h every hour
SELECT add_continuous_aggregate_policy('vehicle_telemetry_first_aggregate_1h',
  start_offset => INTERVAL '1 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 hour');




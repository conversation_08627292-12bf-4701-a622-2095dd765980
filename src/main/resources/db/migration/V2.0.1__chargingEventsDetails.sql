CREATE TABLE charging_event_details (
    timestamp TIMESTAMP NOT NULL,
    vehicle_id BIGINT NOT NULL,
    charging_event_id BIGINT,
    field_name TEXT NOT NULL,
    data_type VARCHAR(255),
    field_value VARCHAR(255) NOT NULL,
    PRIMARY KEY (timestamp, vehicle_id, charging_event_id, field_name),
    CONSTRAINT fk_vehicle_id FOREIGN KEY (vehicle_id) REFERENCES vehicle(id),
    CONSTRAINT fk_charging_event_id FOREIGN KEY (charging_event_id) REFERENCES charging_event(id)
);

CREATE INDEX idx_charging_event_details
ON charging_event_details(vehicle_id, field_name, field_value,charging_event_id)
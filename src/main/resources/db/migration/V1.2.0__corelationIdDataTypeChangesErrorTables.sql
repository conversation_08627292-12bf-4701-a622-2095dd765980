--Change BATTERY ERROR DATA corelation id data type to UUI<PERSON> from varchar
ALTER TABLE vehicle_battery_data_error ADD COLUMN co_relation_id_temp UUID;
-- UPDATE vehicle_battery_data_error SET co_relation_id_temp = co_relation_id::UUID;
-- ALTER TABLE vehicle_battery_data_error DROP COLUMN co_relation_id;
-- ALTER TABLE vehicle_battery_data_error RENAME COLUMN co_relation_id_temp TO co_relation_id;

--Change TELEMETRY ERROR DATA corelation id data type to UUID from varchar
ALTER TABLE vehicle_telemetry_data_error ADD COLUMN co_relation_id_temp UUID;
-- UPDATE vehicle_telemetry_data_error SET co_relation_id_temp = co_relation_id::UUID;
-- ALTER TABLE vehicle_telemetry_data_error DROP COLUMN co_relation_id;
-- ALTER TABLE vehicle_telemetry_data_error RENAME COLUMN co_relation_id_temp TO co_relation_id;

--Change LOCATION ERROR DATA corelation id data type to UUID from varchar
ALTER TABLE vehicle_location_data_error ADD COLUMN co_relation_id_temp UUID;
-- UPDATE vehicle_location_data_error SET co_relation_id_temp = co_relation_id::UUID;
-- ALTER TABLE vehicle_location_data_error DROP COLUMN co_relation_id;
-- ALTER TABLE vehicle_location_data_error RENAME COLUMN co_relation_id_temp TO co_relation_id;


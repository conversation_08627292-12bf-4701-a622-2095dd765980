-- dropping the view_vehicle_daily_usage
drop materialized view if exists view_vehicle_daily_usage;

-- view creation
create materialized view if not exists view_vehicle_daily_usage
as
select
vrm.date,
vrm.imei,
vrm.range,
vrm.usage_time,
vrm.distance,
vs.charging_time from
(select
date(vrm.timestamp) as date,
vrm.imei as imei,
round(sum(distance_travelled)::numeric,2) as distance,
round((sum(distance_travelled)/ NULLIF(sum(CASE WHEN discharge > 0 THEN discharge END),0))::numeric,2) as range,
count(*)*10 as usage_time
from vehicle_running_metrics vrm group by date(vrm.timestamp),vrm.imei) vrm
left join
(select date(vs.timestamp) as date,
vs.imei as imei,
(count(*) * 10) as charging_time
from vehicle_status vs where vehicle_state='CHARGING'
group by date(vs.timestamp),vs.imei) vs
on vrm.date=vs.date and vrm.imei=vs.imei;

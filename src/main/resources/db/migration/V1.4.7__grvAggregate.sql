CREATE MATERIALIZED  VIEW IF NOT EXISTS median_grv_still_1day_aggregate
 WITH (timescaledb.continuous) AS

	SELECT
		imei,
    	time_bucket('1d', timestamp) AS bucket,
		percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_x_axis) AS grv_x_still,
    	percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_y_axis) AS grv_y_still,
    	percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_z_axis) AS grv_z_still
		FROM vehicle_telemetry_data
		WHERE di_ignition = false and di_motion = false and timestamp > '2024-12-02'
	GROUP BY imei,time_bucket ('1d',timestamp) WITH NO DATA;

SELECT add_continuous_aggregate_policy('median_grv_still_1day_aggregate',
  start_offset => INTERVAL '3 day',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '25 minute');



CREATE MATERIALIZED VIEW IF NOT EXISTS median_grv_running_1day_aggregate
   WITH (timescaledb.continuous) AS

  	SELECT
  		imei,
      	time_bucket('1d', timestamp) AS bucket,
  		percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_x_axis) AS grv_x_running,
      	percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_y_axis) AS grv_y_running,
      	percentile_cont(0.5) WITHIN GROUP (ORDER BY grv_z_axis) AS grv_z_running
  		FROM vehicle_telemetry_data
  		WHERE di_ignition = true and di_motion = true and timestamp > '2024-12-02'
  	GROUP BY imei,time_bucket ('1d',timestamp) WITH NO DATA;

  SELECT add_continuous_aggregate_policy('median_grv_running_1day_aggregate',
    start_offset => INTERVAL '3 day',
    end_offset => INTERVAL '1 minute',
    schedule_interval => INTERVAL '25 minute');
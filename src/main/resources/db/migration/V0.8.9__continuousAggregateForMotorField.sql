-- Create the materialized view for 10 sec aggregate
CREATE MATERIALIZED VIEW IF NOT EXISTS view_motor_data_aggregate_per_10sec
WITH (timescaledb.continuous) AS
SELECT
    md.imei,
    md.vehicle_id,
    time_bucket('10s', md.timestamp) AS time_bucket,
    MAX(md.motor_dc_current) AS max_motor_dc_current,
    MIN(md.motor_dc_current) AS min_motor_dc_current,
    MAX(md.motor_dc_voltage) AS max_motor_dc_voltage,
    MIN(md.motor_dc_voltage) AS min_motor_dc_voltage,
    MAX(md.motor_mcs_temperature) AS max_motor_mcs_temperature,
    MIN(md.motor_mcs_temperature) AS min_motor_mcs_temperature
FROM
    vehicle_telemetry_data md
GROUP BY
    md.imei, md.vehicle_id, time_bucket('10s', md.timestamp)
WITH NO DATA;

-- Add continuous aggregate policy
SELECT add_continuous_aggregate_policy('view_motor_data_aggregate_per_10sec',
 start_offset => INTERVAL '1 day',
 end_offset => INTERVAL '1 second',
 schedule_interval => INTERVAL '10 second');

-- Create the materialized view per min aggregate
CREATE MATERIALIZED VIEW IF NOT EXISTS view_motor_data_aggregate_per_1min
WITH (timescaledb.continuous) AS
SELECT
    v1.imei,
    v1.vehicle_id,
    time_bucket('1 minute', v1.time_bucket) AS time_bucket_1min,
    MAX(v1.max_motor_dc_current) AS max_motor_dc_current,
    MIN(v1.min_motor_dc_current) AS min_motor_dc_current,
    MAX(v1.max_motor_dc_voltage) AS max_motor_dc_voltage,
    MIN(v1.min_motor_dc_voltage) AS min_motor_dc_voltage,
    MAX(v1.max_motor_mcs_temperature) AS max_motor_mcs_temperature,
    MIN(v1.min_motor_mcs_temperature) AS min_motor_mcs_temperature
FROM
    view_motor_data_aggregate_per_10sec v1
GROUP BY
    v1.imei, v1.vehicle_id, time_bucket('1 minute', v1.time_bucket)
WITH NO DATA;

-- Add continuous aggregate policy for 1-minute view
SELECT add_continuous_aggregate_policy(
    'view_motor_data_aggregate_per_1min',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '1 second',
    schedule_interval => INTERVAL '1 minute'
);

package com.nichesolv.evahanam.vehicle.listener;

import com.nichesolv.evahanam.common.enums.FeatureName;
import com.nichesolv.evahanam.common.events.VehicleCronEvent;
import com.nichesolv.evahanam.common.events.VehicleEvent;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class GenerateVehicleStatusListener implements ApplicationListener<VehicleCronEvent> {

    @Autowired
    IVehicleService vehicleService;

    @Autowired
    ApplicationEventPublisher publisher;

    @Override
    @Async("cronEventListenerExecutor")
    public void onApplicationEvent(VehicleCronEvent event) {

        if(event == null
                || event.getTime() == null
                || event.getImei() == null
                || event.getFeatureName() == null
                || event.getDataFrequencyPlanDetails() == null) {
            log.error("Received null Time or VehicleCronEvent or DataFrequencyPlanDetails or empty IMEI or FeatureName");
            return;
        }


        if (event.getFeatureName() == FeatureName.VEHICLE_STATUS) {
            VehicleEvent vehicleEvent = vehicleService.saveVehicleState(event.getImei(), event.getTime(), event.getDataFrequencyPlanDetails());
            if(vehicleEvent.getState() == null) {
                return;
            }
            publisher.publishEvent(vehicleEvent);
        } else if (event.getFeatureName() == FeatureName.VEHICLE_STATUS_UPDATION) {
            vehicleService.updateVehicleStateForDataDelay(event.getImei(), event.getTime(), event.getDataFrequencyPlanDetails());

        } else {
            log.debug("Ignoring feature name {} ,it will be taken up be another event listener", event.getFeatureName());
        }
    }
}

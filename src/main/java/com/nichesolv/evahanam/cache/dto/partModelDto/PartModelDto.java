package com.nichesolv.evahanam.cache.dto.partModelDto;

import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PartModelDto {

  Long id;

  String name;

  PartType partType;

  String description;

  Long parentPartModelId;

  Long manufacturerId;

  Set<PartModelAttributeDto> partModelAttributes = new HashSet<>();

}

package com.nichesolv.evahanam.cache.dto.vehicle;

import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import lombok.*;

import java.util.Map;
import java.util.Set;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class VehicleDto {

    Long id;

    String imei;

    String regNo;

    Long vehicleModelId;

    Set<VehiclePartDto> parts;

    Long ownerId;

    Long dealershipId;

    Long manufacturerId;

    OperationStatus operationStatus;

    String chassisNumber;

    String encryptionKey;

    String deviceAdvertisingName;

    VehicleState vehicleState;

    Map<String,Long> subscriptions;
}

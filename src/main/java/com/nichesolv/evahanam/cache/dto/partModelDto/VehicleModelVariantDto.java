package com.nichesolv.evahanam.cache.dto.partModelDto;

import lombok.*;

import java.util.*;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class VehicleModelVariantDto extends PartModelDto {

    String modelNo;

    Set<PartModelDto> partModels = new HashSet<>();

    String weightUom;

    Float grossWeight;

    Float netWeight;

    Set<VehicleImageDto> images;

    Set<DriveModeSpeedDto> driveModes = new HashSet<>();

    public String constructKey() {
        final String[] key = {this.getName()};
        List<PartModelDto> partModelDtoList =
                partModels.stream().sorted(Comparator.comparing(PartModelDto::getPartType))
                        .toList();
        partModelDtoList.forEach(e -> {
            key[0] = key[0].concat("::").concat("[").concat(e.getPartType().name()).concat("::").concat(e.getName()).concat("]");
        });
        return key[0];
    }

}

package com.nichesolv.evahanam.cache.handler;


import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.nichesolv.evahanam.cache.service.VehicleImeiGrvAggCacheRefreshService;

@Component("IMEI_GRV_AGG")
@Slf4j
public class VehicleImeiGrvAggHandler implements EventHandler {
    @Autowired
    private VehicleDataRepository vehicleRepository;

    @Autowired
    private VehicleImeiGrvAggCacheRefreshService imeiGrvAggService;

    @Override
    public void handleEvent(String[] keyValueArray, String eventName) {
        if (keyValueArray.length == 2 && "keymiss".equals(eventName)) {
            String imei = keyValueArray[1];
            imeiGrvAggService.handleGrvAggregateMiss(imei);
            return;
        }
        log.debug("THIS EVENT IS {}", eventName);
        if (keyValueArray.length == 2 && "expired".equals(eventName)) {
            String imei = keyValueArray[1];
            imeiGrvAggService.handleGrvAggregateExpire(imei);
        }
    }
}

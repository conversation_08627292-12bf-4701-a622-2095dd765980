package com.nichesolv.evahanam.cache.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class EventHandlerFactory {


    private final Map<String, EventHandler> eventHandlers;

    @Autowired
    public EventHandlerFactory(Map<String, EventHandler> eventHandlers) {
        this.eventHandlers = eventHandlers;
    }

    public EventHandler getHandler(String keyType) {
        return eventHandlers.get(keyType);
    }
}

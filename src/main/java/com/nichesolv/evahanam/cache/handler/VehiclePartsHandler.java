package com.nichesolv.evahanam.cache.handler;


import com.nichesolv.evahanam.cache.service.VehicleCacheRefreshService;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("VEHICLE_PARTS")
@Slf4j
public class VehiclePartsHandler implements EventHandler {
    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private VehicleCacheRefreshService vehicleCacheRefreshService;

    @Override
    public void handleEvent(String[] keyValueArray, String eventName) {
        log.info("in VEHICLE_PARTS KEY:: Plan requested with k0 : {}, k1 {} ,eventName : {}", keyValueArray[0], keyValueArray[1], eventName);
        if (keyValueArray.length == 2 && eventName.equals("keymiss")) {
            String imei = keyValueArray[1];
            vehicleRepository.findByImei(imei).ifPresentOrElse(vehicle -> {
                vehicleCacheRefreshService.initializeOrUpdateVehiclePartCache(List.of(vehicle));
            }, () -> {
                log.debug("VEHICLE_PARTS:: IMEI not found with imei : {}, eventName : {}", imei, eventName);
            });
        }
    }
}

package com.nichesolv.evahanam.cache.handler;


import com.nichesolv.evahanam.cache.enums.KeyType;
import com.nichesolv.evahanam.cache.service.SubscriptionCacheService;
import com.nichesolv.evahanam.cache.service.VehicleCacheRefreshService;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("SUBSCRIPTIONS_FREQUENCY")
@Slf4j
public class SubscriptionFrequencyHandler implements EventHandler {

    @Autowired
    private SubscriptionCacheService subscriptionCacheService;

    @Override
    public void handleEvent(String[] keyValueArray, String eventName) {
        log.info("IN SUBSCRIPTIONS KEY:: Plan requested with k0 : {}, k1 {} ,eventName : {}", keyValueArray[0], keyValueArray[1], eventName);
        if (keyValueArray.length == 2 && eventName.equals("keymiss")) {
            subscriptionCacheService.initOrUpdateSubscription(keyValueArray[1], KeyType.valueOf(keyValueArray[0]));
        } else {
            log.debug("UNKNOWN SUBSCRIPTIONS KEY:: Plan not found with id : {}, eventName : {}", keyValueArray[1], eventName);
        }
    }
}

package com.nichesolv.evahanam.cache.handler;


import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.nichesolv.evahanam.cache.service.VehicleImeiGrvRawAggCacheRefreshService;

@Component("IMEI_GRV_RAW_AGG")
@Slf4j
public class VehicleImeiGrvRawAggHandler implements EventHandler {
    @Autowired
    private VehicleDataRepository vehicleRepository;

    @Autowired
    private VehicleImeiGrvRawAggCacheRefreshService imeiGrvAggService;

    @Override
    public void handleEvent(String[] keyValueArray, String eventName) {
        if (keyValueArray.length == 2 && "keymiss".equals(eventName)) {
            String imei = keyValueArray[1];
            imeiGrvAggService.handleGrvRawAggregateMiss(imei);
            return;
        }
        if (keyValueArray.length == 2 && "expired".equals(eventName)) {
            String imei = keyValueArray[1];
            imeiGrvAggService.handleGrvRawAggregateExpire(imei);
        }
    }
}

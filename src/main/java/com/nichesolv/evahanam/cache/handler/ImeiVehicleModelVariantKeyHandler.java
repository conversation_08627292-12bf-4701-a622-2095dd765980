package com.nichesolv.evahanam.cache.handler;

import com.nichesolv.evahanam.cache.service.VehicleCacheRefreshService;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("IMEI_VEHICLE_MODEL_VARIANT_KEY")
@Slf4j
public class ImeiVehicleModelVariantKeyHandler implements EventHandler {
    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private VehicleCacheRefreshService vehicleCacheRefreshService;

    @Override
    public void handleEvent(String[] keyValueArray, String eventName) {
        if (keyValueArray.length > 1 && eventName.equals("keymiss")) {
            String imei = keyValueArray[1];
            vehicleRepository.findByImei(imei).ifPresentOrElse(vehicle -> {
                vehicleCacheRefreshService.initializeOrUpdateImeiToVariantKeyCache(List.of(vehicle));
            }, () -> {
                log.debug("IMEI_VEHICLE_MODEL_VARIANT_KEY : IMEI not found with imei : {}, eventName : {}", imei, eventName);
            });
        }
    }
}

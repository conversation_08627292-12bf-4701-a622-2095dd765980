package com.nichesolv.evahanam.cache.handler;


import com.nichesolv.evahanam.cache.service.VehicleCacheRefreshService;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("IMEI_VEHICLE")
@Slf4j
public class ImeiVehicleHandler implements EventHandler {
    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private VehicleCacheRefreshService vehicleCacheRefreshService;

    @Override
    public void handleEvent(String[] keyValueArray, String eventName) {
        if (keyValueArray.length == 2 && eventName.equals("keymiss")) {
            String imei = keyValueArray[1];
            vehicleRepository.findByImei(imei).ifPresentOrElse(vehicle -> {
                vehicleCacheRefreshService.initializeOrUpdateVehicleCache(List.of(vehicle));
            }, () -> {
                log.debug("IMEI_VEHICLE:: IMEI not found with imei : {}, eventName : {}", imei, eventName);
            });
        }
    }
}

package com.nichesolv.evahanam.common.events;

import com.nichesolv.evahanam.common.enums.FeatureName;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import com.nichesolv.evahanam.vehicle.dto.CronDataSource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;

import java.time.Clock;
import java.time.Instant;


@Getter
@Setter
@Slf4j
public class VehicleCronEvent extends ApplicationEvent {

    String imei;
    Instant time;
    DataFrequencyPlanDetails dataFrequencyPlanDetails;
    FeatureName featureName;

    public VehicleCronEvent(String imei, Instant time, DataFrequencyPlanDetails dataFrequencyPlanDetails) {
        super(imei);
        this.imei = imei;
        this.time = time;
        this.dataFrequencyPlanDetails = dataFrequencyPlanDetails;
        this.featureName = dataFrequencyPlanDetails.getFeatureName();
    }
}

package com.nichesolv.evahanam.common.events;

import com.nichesolv.evahanam.trip.jpa.VehicleEventMonitor;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;


@Getter
@Setter
@Slf4j
public class VehicleStateEvent extends ApplicationEvent {

    VehicleEventMonitor eventMonitor;

    VehicleStatus vehicleStatus;

    public VehicleStateEvent(VehicleEventMonitor eventMonitor, VehicleStatus vehicleStatus) {
        super(eventMonitor);
        this.vehicleStatus = vehicleStatus;
        this.eventMonitor = eventMonitor;
    }


}

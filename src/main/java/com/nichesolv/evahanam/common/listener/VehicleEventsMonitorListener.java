package com.nichesolv.evahanam.common.listener;

import com.nichesolv.evahanam.common.events.VehicleEvent;
import com.nichesolv.evahanam.common.events.VehicleStateEvent;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatusIdx;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Component
@Slf4j
public class VehicleEventsMonitorListener implements ApplicationListener<VehicleEvent> {


    @Autowired
    IVehicleService vehicleService;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;

    @Autowired
    ApplicationEventPublisher publisher;

    @Override
    @Async("cronEventListenerExecutor")
    public void onApplicationEvent(VehicleEvent event) {
        try {
            Optional<VehicleStatus> vehicleStatus = vehicleStatusRepository.findById(new VehicleStatusIdx(event.getTime(), event.getImei()));

            if(vehicleStatus.isEmpty()){
                log.warn("VehicleStatus not found for imei: {} at time: {}", event.getImei(), event.getTime());
                return;
            }
            Vehicle vehicle = vehicleService.getVehicleByAnyId(event.getImei());
            /*
            On Vehicle Running Event, update the vehicle running metrics and odometer in vehicle latest data
             */

            //updating the vehicle_event_monitor table

            VehicleStateEvent vehicleStateEvent = vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus.get());
            if( vehicleStateEvent == null || vehicleStateEvent.getVehicleStatus() == null) {
                log.warn("VehicleStateEvent has no VehicleStatus for imei: {}", event.getImei());
                return;
            }
            publisher.publishEvent(vehicleStateEvent);
        } catch (Exception e) {
            log.error("Error in updating the vehicle_event_monitor with message ", e);
        }

    }
}

package com.nichesolv.evahanam.trip.jpa;
import com.nichesolv.evahanam.common.enums.EventType;
import com.nichesolv.evahanam.trip.enums.EventConstants;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "event_duration_constants",
        uniqueConstraints = {
        @UniqueConstraint(columnNames = {"event_constant","event_type", "org_id"})
})
/**
 * This Entity class is to define the trip constants (MIN_TRIP_DURATION/STOP DURATION for an organisation)
 */
public class EventDurationConstants {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_constant")
    EventConstants constant;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type")
    EventType type;

    @NotNull
    Integer duration;

    String unit;

    @OneToOne
    @JoinColumn(name = "org_id")
    CustomOrganisation organisation;

    public EventDurationConstants(EventConstants tripConstant, Integer duration, String unit, CustomOrganisation organisation) {
        this.constant = tripConstant;
        this.duration = duration;
        this.unit = unit;
        this.organisation = organisation;
        this.type  = EventType.TRIP; // Defaulting to TRIP type, can be changed as needed
    }

    public EventDurationConstants(EventConstants eventConstant, EventType type, Integer duration, String unit, CustomOrganisation organisation) {
        this.constant = eventConstant;
        this.type = type;
        this.duration = duration;
        this.unit = unit;
        this.organisation = organisation;
    }
}

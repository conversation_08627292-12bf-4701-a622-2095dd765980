package com.nichesolv.evahanam.trip.repository;

import com.nichesolv.evahanam.common.enums.EventType;
import com.nichesolv.evahanam.trip.enums.EventConstants;
import com.nichesolv.evahanam.trip.jpa.EventDurationConstants;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EventDurationConstantsRepository extends JpaRepository<EventDurationConstants, Long> {

    Optional<EventDurationConstants> findByOrganisationAndConstantAndType(CustomOrganisation organisation, EventConstants tripConstant, EventType eventType);
}

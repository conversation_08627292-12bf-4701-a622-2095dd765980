package com.nichesolv.evahanam.trip.listener;

import com.nichesolv.evahanam.common.enums.EventType;
import com.nichesolv.evahanam.common.events.VehicleStateEvent;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.EventConstants;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.jpa.EventDurationConstants;
import com.nichesolv.evahanam.trip.jpa.VehicleEventMonitor;
import com.nichesolv.evahanam.trip.repository.EventDurationConstantsRepository;
import com.nichesolv.evahanam.trip.service.TripSummaryService;

import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicleTests.exception.TripConstantNotFoundException;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Slf4j
public class TripEventListener implements ApplicationListener<VehicleStateEvent> {


    @Autowired
    TripSummaryService tripSummaryService;

    @Autowired
    EventDurationConstantsRepository tripDurationConstantsRepository;

    @Autowired
    EvMessageBundle evMessageBundle;



    @Override
    @Async("cronEventListenerExecutor")
    @Transactional
    public void onApplicationEvent(VehicleStateEvent event) {
        if (event == null || event.getEventMonitor() == null || event.getVehicleStatus() == null) {
            log.warn("Received null VehicleMonitorEvent, skipping trip processing.");
            return;
        }
        VehicleStatus vehicleStatus = event.getVehicleStatus();
        if (vehicleStatus.getVehicleState() == VehicleState.CHARGING) {
            log.debug("Vehicle {} is in CHARGING state, skipping trip processing.", vehicleStatus.getVehicle().getImei());
            return;
        }
        VehicleEventMonitor vehicleEventMonitor = event.getEventMonitor();
        Vehicle vehicle = vehicleEventMonitor.getVehicle();
        Long runningTime = vehicleEventMonitor.getRunningTime();
        Long stoppageTime = vehicleEventMonitor.getStoppageTime();
        EventDurationConstants minTripDurationConstant = tripDurationConstantsRepository.findByOrganisationAndConstantAndType((CustomOrganisation) vehicle.getManufacturer(), EventConstants.MIN_TRIP_DURATION, EventType.TRIP).orElseThrow(() -> new TripConstantNotFoundException(evMessageBundle.getMessage("TRIP_CONSTANT_NOT_FOUND", EventConstants.MIN_TRIP_DURATION.name(), vehicle.getManufacturer().getId())));
        EventDurationConstants stopDurationConstant = tripDurationConstantsRepository.findByOrganisationAndConstantAndType((CustomOrganisation) vehicle.getManufacturer(), EventConstants.STOP_DURATION, EventType.TRIP).orElseThrow(() -> new TripConstantNotFoundException(evMessageBundle.getMessage("TRIP_CONSTANT_NOT_FOUND", EventConstants.STOP_DURATION.name(), vehicle.getManufacturer().getId())));
        List<Trip> inProgressTrip = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC);

        TestRideSummaryPopulationStatus status = TestRideSummaryPopulationStatus.FAILED ;
        //saving or updating the trip
        if (runningTime >= minTripDurationConstant.getDuration() && inProgressTrip.isEmpty()) {
            status = TestRideSummaryPopulationStatus.IN_PROGRESS;
            tripSummaryService.saveAutomaticTrips(vehicle, runningTime, stoppageTime, status, vehicleStatus.getVehicleStatusIdx().getTimestamp(), inProgressTrip);
        } else if (stoppageTime >= stopDurationConstant.getDuration() && !inProgressTrip.isEmpty()) {
            status =  TestRideSummaryPopulationStatus.COMPLETED;
            tripSummaryService.saveAutomaticTrips(vehicle, runningTime, stoppageTime, status, vehicleStatus.getVehicleStatusIdx().getTimestamp(), inProgressTrip);
        }else{
            log.debug("Status of trip for vehicle {} is {}, not saving trip", vehicle.getImei(), status);
            return;
        }



    }
}

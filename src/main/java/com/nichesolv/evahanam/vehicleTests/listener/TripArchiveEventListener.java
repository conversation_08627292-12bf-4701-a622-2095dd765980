package com.nichesolv.evahanam.vehicleTests.listener;

import com.nichesolv.evahanam.common.events.CronEvent;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.vehicle.dto.CronDataSource;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
public class TripArchiveEventListener implements ApplicationListener<CronEvent>{



    @Autowired
    VehicleDataRepository vehicleDataRepository;

    @Autowired
    VehicleTestRepository vehicleTestRepository;

    @Async("cronEventListenerExecutor")
    @Override
    public void onApplicationEvent(CronEvent event) {
        CronDataSource data = event.getSource();
        LocalDateTime currentLocalDataTime = data.getCurrentLocalTime();

        if(currentLocalDataTime.getHour() == 12 && currentLocalDataTime.getMinute() == 30 && currentLocalDataTime.getSecond() ==0)
        {
            archiveOldTests();
        }else {
            log.debug("Skipping test archival as the current time is not 12:30:00");
        }

    }

    /**
     * This method archives the tests which are older than the data present in the db.
     */
    @Transactional
    public void archiveOldTests() {
        log.info("Starting scheduled test archival process");
        try {
            Instant earliestTimestamp = vehicleDataRepository.findEarliestTimestamp();
            if (earliestTimestamp != null) {
                // Archive only the tests that are COMPLETED and created before the earliest timestamp
                List<VehicleTest> oldTests = vehicleTestRepository.findByCreatedOnBeforeAndStatus(earliestTimestamp, TestStatus.COMPLETED);
                if (!oldTests.isEmpty()) {
                    oldTests.forEach(test -> test.setStatus(TestStatus.ARCHIVED));
                    vehicleTestRepository.saveAll(oldTests);
                    log.info("Archived {} tests successfully", oldTests.size());
                } else {
                    log.info("No completed tests found for archival");
                }
            } else {
                log.warn("Could not determine earliest timestamp for archival");
            }
        } catch (Exception e) {
            log.error("Exception during test archival : {}", e.getMessage(), e);
        }
    }
}
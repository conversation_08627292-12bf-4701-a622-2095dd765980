package com.nichesolv.evahanam.vehicleTests.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Point;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmergencyEventDto {
    Long emergencyEventId;
    Long vehicleId;
    Long userId;
    String userName;
    Long mfrOrgId;
    String mfrOrgName;
    String emergencyType;
    String emergencyStatus;
    Instant registeredOn;
    Instant fixedOn;
    String text;

}

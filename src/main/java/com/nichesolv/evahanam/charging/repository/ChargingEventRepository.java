package com.nichesolv.evahanam.charging.repository;

import com.nichesolv.evahanam.charging.enums.ChargingStatus;
import com.nichesolv.evahanam.charging.jpa.ChargingEvent;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

// ChargingEventRepository.java
public interface ChargingEventRepository extends JpaRepository<ChargingEvent,Long> {
    Optional<ChargingEvent> findByVehicleAndStatus(Vehicle vehicle, ChargingStatus status);

    int countByVehicleAndStatus(Vehicle vehicle, ChargingStatus status);
}

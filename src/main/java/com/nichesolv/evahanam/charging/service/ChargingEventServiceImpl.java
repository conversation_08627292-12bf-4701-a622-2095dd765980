package com.nichesolv.evahanam.charging.service;

import com.nichesolv.evahanam.charging.enums.ChargingStatus;
import com.nichesolv.evahanam.charging.jpa.ChargingEvent;
import com.nichesolv.evahanam.charging.jpa.ChargingEventDetails;
import com.nichesolv.evahanam.charging.jpa.EventIdx;
import com.nichesolv.evahanam.charging.repository.ChargingEventDetailsRepository;
import com.nichesolv.evahanam.charging.repository.ChargingEventRepository;
import com.nichesolv.evahanam.telemetryData.enums.ExportDataType;
import com.nichesolv.evahanam.telemetryData.service.vehicleData.VehicleDataService;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicleTests.v2.dto.FieldListDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class ChargingEventServiceImpl implements ChargingEventService {

    @Autowired
    ChargingEventRepository chargingEventRepository;

    @Autowired
    ChargingEventDetailsRepository chargingEventDetailsRepository;

    @Autowired
    VehicleDataService dataService;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;
    /**
     * Starts a new charging event for the given vehicle.
     *
     * @param vehicle   the vehicle for which the charging event is to be started
     * @param startTime the time when the charging started
     * @return the created ChargingEvent with status set to IN_PROGRESS
     */
    @Transactional
    @Override
    public void startChargingEvent(Vehicle vehicle, Instant startTime){
        try {
            ChargingEvent chargingEvent = new ChargingEvent(vehicle, startTime);
            chargingEventRepository.save(chargingEvent);
            /**
             * Save charging event at the start of charging into charging event details table.
             * Also save location details if available.
             */
            List<ChargingEventDetails> chargingEventDetailsList = getChargingEventDetails(vehicle, startTime, chargingEvent,EventStatus.START);
            chargingEventDetailsRepository.saveAll(chargingEventDetailsList);


        } catch (Exception e) {
            log.error("Error starting charging event for vehicle {}", vehicle.getImei());
        }
    }


    private List<ChargingEventDetails> getChargingEventDetails(Vehicle vehicle, Instant startTime, ChargingEvent chargingEvent,EventStatus eventStatus) {
        List<String> batteryColumns = dataService.getColumnName(ExportDataType.BATTERY);
        Optional<VehicleStatus> endingVehicleStatus = vehicleStatusRepository
                .findFirstByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampGreaterThanOrderByVehicleStatusIdxTimestampDesc(vehicle.getImei(), startTime);
        Instant endTime = chargingEvent.getStatus() == ChargingStatus.COMPLETED?
                endingVehicleStatus.get().getVehicleStatusIdx().getTimestamp()
                :startTime;
        FieldListDto fieldListDto = new FieldListDto();
        fieldListDto.setField(batteryColumns);
        List<ChargingEventDetails> chargingEventDetailsList = new ArrayList<>();
        Optional<Map<String,Object>> batteryDataAtStart = dataService
                .getData(vehicle.getId().toString(),Optional.empty()
                        ,Optional.of(startTime.minus(10, ChronoUnit.SECONDS).toEpochMilli())
                        ,Optional.of(endTime.toEpochMilli())
                        , ExportDataType.BATTERY
                        ,fieldListDto,
                        null,
                        false)
                .getData()
                .stream()
                .filter(e-> e.get("soc") != null && e.get("remaining_capacity") != null)
                .filter(map -> fieldListDto.getField().stream().allMatch(map::containsKey))
                .max((a, b) -> {
                    Long t1 = (Long) a.get("timestamp");
                    Long t2 = (Long) b.get("timestamp");
                    return Long.compare(t1,t2);
                });
        batteryDataAtStart
                .ifPresent(map->map
                        .keySet()
                        .stream()
                        .filter(e->!e.equals("timestamp") && !e.equals("imei"))
                        .filter(e->map.containsKey(e)).  forEach


        (e->{
            if(map.get(e) != null) {
                EventIdx eventIdx = new EventIdx(startTime, vehicle, chargingEvent, eventStatus.name().toLowerCase().concat("_"+e));
                ChargingEventDetails chargingEventDetails = new ChargingEventDetails(eventIdx, "Number", map.getOrDefault(e, 0).toString());
                chargingEventDetailsList.add(chargingEventDetails);
            }
                }));
        return chargingEventDetailsList;
    }

    /**
     * Stops a charging event by its ID.
     *
     * @param chargingEvent the ID of the charging event to stop
     * @param endTime         the time when the charging ended
     * @return the updated ChargingEvent with status set to COMPLETED
     */
    @Override
    @Transactional
    public ChargingEvent stopChargingEvent(ChargingEvent chargingEvent, Instant endTime){
        try {
            chargingEvent.setEndTime(endTime);
            chargingEvent.setStatus(ChargingStatus.COMPLETED);
            chargingEventRepository.save(chargingEvent);
            /**
             * Save charging event at the end of charging into charging event details table.
             */
            List<ChargingEventDetails> chargingEventDetailsList = getChargingEventDetails(chargingEvent.getVehicle(), endTime, chargingEvent,EventStatus.END);
            chargingEventDetailsRepository.saveAll(chargingEventDetailsList);
        } catch (Exception e) {
            log.error("Error stopping charging event for vehicle {}", chargingEvent.getVehicle().getImei(), e);
        }
            return chargingEvent;
    }


    /**
     * Retrieves the latest charging event for a given vehicle.
     *
     * @param vehicle the vehicle for which to retrieve the latest charging event
     * @return the latest ChargingEvent for the vehicle, or null if no events exist
     */
    public Optional<ChargingEvent> getLatestChargingEventByVehicle(Vehicle vehicle){
        return chargingEventRepository.findByVehicleAndStatus(vehicle,ChargingStatus.IN_PROGRESS);
    }

    /**
     * Finds charging events by vehicle and status.
     *
     * @param vehicle the vehicle for which to find charging events
     * @param status  the status of the charging events to find
     * @return a list of ChargingEvent objects matching the criteria
     */
    @Override
    public int countByVehicleAndStatus(Vehicle vehicle, ChargingStatus status) {
        return chargingEventRepository.countByVehicleAndStatus(vehicle, status);
    }
    enum EventStatus{START,END}
}

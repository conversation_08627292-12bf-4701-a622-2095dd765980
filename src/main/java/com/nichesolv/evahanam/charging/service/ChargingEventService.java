package com.nichesolv.evahanam.charging.service;

import com.nichesolv.evahanam.charging.enums.ChargingStatus;
import com.nichesolv.evahanam.charging.jpa.ChargingEvent;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface ChargingEventService {

    /**
     * Starts a new charging event for the given vehicle.
     *
     * @param vehicle   the vehicle for which the charging event is to be started
     * @param startTime the time when the charging started
     * @return the created ChargingEvent with status set to IN_PROGRESS
     */
    void startChargingEvent(Vehicle vehicle, Instant startTime);

    /**
     * Stops a charging event by its ID.
     *
     * @param chargingEvent the ID of the charging event to stop
     * @param endTime         the time when the charging ended
     * @return the updated ChargingEvent with status set to COMPLETED
     */
    ChargingEvent stopChargingEvent(ChargingEvent chargingEvent, Instant endTime);

    /**
     * Retrieves the latest charging event for a given vehicle.
     *
     * @param vehicle the vehicle for which to retrieve the latest charging event
     * @return the latest ChargingEvent for the vehicle, or null if no events exist
     */
    Optional<ChargingEvent> getLatestChargingEventByVehicle(Vehicle vehicle);

    int countByVehicleAndStatus(Vehicle vehicle, ChargingStatus status);
}

package com.nichesolv.evahanam.charging.jpa;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;


@AllArgsConstructor
@NoArgsConstructor
@Entity
@Getter
@Setter
@Table(name = "charging_event_details")
public class ChargingEventDetails {

    @EmbeddedId
    EventIdx id;

    String dataType;

    @NotEmpty(message = "field value cannot be null")
    String fieldValue;

}

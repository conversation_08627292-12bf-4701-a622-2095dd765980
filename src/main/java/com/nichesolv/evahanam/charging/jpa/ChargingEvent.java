package com.nichesolv.evahanam.charging.jpa;

import com.nichesolv.evahanam.charging.enums.ChargeType;
import com.nichesolv.evahanam.charging.enums.ChargingStatus;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.vehicle.enums.UpdateSource;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.locationtech.jts.geom.Point;

import java.time.Instant;


@AllArgsConstructor
@NoArgsConstructor
@Entity
@Data
@Table(name = "charging_event",
        indexes = {
                @Index(name = "charging_event_search_idx", columnList = "vehicle_id,status,imei,charge_type,start_time"),
        }
)
public class ChargingEvent {

    public ChargingEvent(Vehicle vehicle, Instant startTime) {
        this.vehicle = vehicle;
        this.startTime = startTime;
        this.status = ChargingStatus.IN_PROGRESS;
        this.updateSource = UpdateSource.VEHICLE_STATUS_CRON;
        this.chargeType = ChargeType.NORMAL;
        this.imei = vehicle.getImei();
        this.user = (CustomUser)vehicle.getUser();
        this.ownerOrg = vehicle.getOwner();
        this.mfrOrg = vehicle.getManufacturer();

    }

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "charging_event_seq")
    @SequenceGenerator(name = "charging_event_seq", sequenceName = "charging_event_seq", allocationSize = 1)
    Long id;

    @NotEmpty(message = "Imei cannot be null")
    String imei;

    @ManyToOne(optional = true)
    @JoinColumn(foreignKey = @ForeignKey(name = "fk_user_id"))
    CustomUser user;

    @ManyToOne(optional = false)
    @JoinColumn(foreignKey = @ForeignKey(name = "fk_vehicle_id"))
    Vehicle vehicle;

    @Column(name = "start_time")
    @NotNull(message = "StartTime cannot be Empty")
    Instant startTime;

    Instant endTime;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    ChargingStatus status;

    @ManyToOne(targetEntity = CustomOrganisation.class)
    @JoinColumn(name = "owner_org_id")
    Organisation ownerOrg;

    @ManyToOne(targetEntity = CustomOrganisation.class)
    @JoinColumn(name = "mfr_org_id")
    Organisation mfrOrg;

    @CreationTimestamp
    Instant createdOn;

    @UpdateTimestamp
    Instant updatedOn;

    @Enumerated(EnumType.STRING)
    UpdateSource updateSource;

    @Column(name = "location",columnDefinition = "geography(PointZM,4326)")
    Point location;

    String neighbourhood;
    String suburb;
    String city;
    String state;

    @Column(name = "charge_type")
    @Enumerated(EnumType.STRING)
    ChargeType chargeType;

}

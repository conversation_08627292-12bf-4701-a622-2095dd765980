package com.nichesolv.evahanam.charging.jpa;

import com.nichesolv.evahanam.charging.enums.ChargeType;
import com.nichesolv.evahanam.charging.enums.ChargingStatus;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.vehicle.enums.UpdateSource;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.locationtech.jts.geom.Point;

import java.time.Instant;


@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class EventIdx {

    @Column(nullable = false)
    Instant timestamp;

    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(name = "fk_vehicle_id"))
    Vehicle vehicle;

    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(name = "fk_charging_event_id"))
    ChargingEvent chargingEvent;

    @NotEmpty(message = "Field name cannot be null")
    @Column(columnDefinition = "TEXT", nullable = false)
    String fieldName;
}

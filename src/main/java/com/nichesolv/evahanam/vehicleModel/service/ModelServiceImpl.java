package com.nichesolv.evahanam.vehicleModel.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nichesolv.evahanam.common.dto.UserAuthTokenContext;
import com.nichesolv.evahanam.common.repository.ImageRepository;
import com.nichesolv.evahanam.common.repository.MyCustomOrganisationRepository;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.evApp.exception.OrganisationNotFoundException;
import com.nichesolv.evahanam.vehicle.dto.ImageDto;
import com.nichesolv.evahanam.vehicle.dto.PartModelAttributeProjection;
import com.nichesolv.evahanam.vehicle.exception.OrganisationException;
import com.nichesolv.evahanam.vehicle.exception.PartException;
import com.nichesolv.evahanam.vehicle.exception.VehicleNotFoundException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleModel.dto.*;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.events.VehicleModelCreatedEvent;
import com.nichesolv.evahanam.vehicleModel.exception.DriveModeException;
import com.nichesolv.evahanam.vehicleModel.exception.PartModelException;
import com.nichesolv.evahanam.vehicleModel.jpa.*;
import com.nichesolv.evahanam.vehicleModel.repository.*;
import com.nichesolv.nds.dto.organisation.response.OrganisationDto;
import com.nichesolv.nds.dto.organisation.response.OrganisationDtoMapper;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.repository.CustomOrganisationProfileRepository;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ModelServiceImpl implements ModelService {

    @Autowired
    PartModelRepository partModelRepository;

    @Autowired
    MyCustomOrganisationRepository myCustomOrganisationRepository;
    @Autowired
    CustomOrganisationProfileRepository customOrganisationProfileRepository;

    @Autowired
    ImageRepository imageRepository;
    @Autowired
    PartModelAttributeRepository partModelAttributeRepository;

    @Autowired
    ModelRepository modelRepository;
    @Autowired
    UserAuthTokenContext userAuthToken;

    @Autowired
    VehicleModelRepository vehicleModelRepository;

    @Autowired
    OrganisationDtoMapper organisationDtoMapper;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    DriveModeRepository driveModeRepository;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    DriveModeMaxRangeRepository driveModeMaxRangeRepository;

    static ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private ApplicationEventPublisher eventPublisher;


    
    @Override
    public List<com.nichesolv.evahanam.vehicle.dto.VehicleModelDto> getAllVehicleModel(Organisation organisation, Optional<Long> orgId) {
        if(orgId.isPresent()){
            organisation=organisationRepository.findById(orgId.get()).orElseThrow(()->new OrganisationException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
        }
        List<com.nichesolv.evahanam.vehicle.dto.VehicleModelDto> result= new ArrayList<>();
        List<VehicleModel> vehicleModelList= vehicleModelRepository.findByManufacturer(organisation);
        vehicleModelList.forEach(e->{
            com.nichesolv.evahanam.vehicle.dto.VehicleModelDto vehicleModelDto=new com.nichesolv.evahanam.vehicle.dto.VehicleModelDto();
            BeanUtils.copyProperties(e,vehicleModelDto);
            result.add(vehicleModelDto);
        });
        return result;
    }
    public ModelServiceImpl(PartModelRepository partModelRepository,
                            VehicleModelRepository vehicleModelRepository) {
        this.partModelRepository = partModelRepository;
        this.vehicleModelRepository = vehicleModelRepository;
    }

    public Optional<PartModelAttribute> getPartModelAttribute(Vehicle vehicle, PartType partType, String attributeName) {
        DataTypeLongProjection partModelId = vehicleRepository
                .getPartModelIdByVehicleIdAndPartType(vehicle.getId(), partType.toString())
                .orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("PART_MODEL_ID_NOT_FOUND", partType)));

        // Fetch the battery model by ID and handle exceptions
        PartModel partModel = partModelRepository
                .findById(partModelId.getId())
                .orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("PART_MODEL_ID_NOT_FOUND", partType.toString())));

        // Retrieve the fullCapacity attribute if present
        return partModel.getPartModelAttributes().stream()
                .filter(attribute -> attributeName.equals(attribute.getName()))
                .findFirst();
    }
    @Override
    @Transactional
    public PartModel convertAndSavePart(PartModelDto modelDto) {
        PartModel model = new PartModel();
        PartType partType = PartType.valueOf(modelDto.getPartType());
        if (partType.equals(PartType.BATTERY)) {
            model = BatteryModel.convertFromPartModel(getNewPartModel(modelDto));
        } else if (partType.equals(PartType.MCU)) {
            model = McuModel.convertFromPartModel(getNewPartModel(modelDto));
        } else if (partType.equals(PartType.REAR_TYRE)) {
            model = RearTyreModel.convertFromPartModel(getNewPartModel(modelDto));
        } else if (partType.equals(PartType.COLOR)) {
            model = ColorModel.convertFromPartModel(getNewPartModel(modelDto));
        } else if (partType.equals(PartType.TCU)) {
            model = TcuModel.convertFromPartModel(getNewPartModel(modelDto));
        } else if (partType.equals(PartType.MOTOR)) {
            model = MotorModel.convertFromPartModel(getNewPartModel(modelDto));
        } else if (partType.equals(PartType.GSM)) {
            model = GsmModel.convertFromPartModel(getNewPartModel(modelDto));
        } else {
            // Default case when we don't have a specific model for the part type
            // enum will make sure part types other than that in our list cannot be added
            model = getNewPartModel(modelDto);
        }
        return (PartModel) modelRepository.save(model);
    }

    @Transactional
    private PartModel getNewPartModel(PartModelDto modelDto) {
        Optional<PartModel> partModel = Optional.ofNullable(modelDto.getParentPartModelId()).isPresent() ? Optional.ofNullable(modelDto.getParentPartModelId()).map(
                e -> partModelRepository.findById(e)
                        .orElseThrow(() -> new PartException(evMessageBundle.getMessage("PARENT_ID_NOT_FOUND")))) : Optional.empty();

        CustomOrganisation manufacturer = Optional.ofNullable(organisationRepository.findByName(modelDto.getManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));

        if (partModelRepository.findByNameIgnoreCaseAndManufacturer(modelDto.getName(), manufacturer).isPresent()) {
            throw new PartException(evMessageBundle.getMessage("PART_EXISTS_WITH_NAME_AND_MFR", modelDto.getName(), modelDto.getManufacturerName()));
        }

        PartModel model = new PartModel(PartType.valueOf(modelDto.getPartType()), modelDto.getName(),
                modelDto.getDescription(), partModel.orElse(null), manufacturer, null);

        Set<PartModelAttribute> partModelAttributes = new HashSet<>();
        if (Optional.ofNullable(modelDto.getAttributes()).isPresent()) {
            partModelAttributes = getPartModelAttributes(modelDto.getAttributes(), partModelAttributes);
        }
        model.setPartModelAttributes(partModelAttributes);
        return model;
    }
    @Transactional
    private Set<PartModelAttribute> getPartModelAttributes(List<Map<String, String>> attributes, Set<PartModelAttribute> partModelAttributes) {

        attributes.stream().forEach(
                attribute -> {
                    PartModelAttribute partModelAttribute = new PartModelAttribute();

                    attribute.entrySet().forEach(e -> {
                        if (e.getKey().equals("type")) {
                            partModelAttribute.setType(e.getValue());
                        } else if (e.getKey().equals("name")) {
                            partModelAttribute.setName(e.getValue());
                        } else if (e.getKey().equals("value")) {
                            partModelAttribute.setValue(e.getValue());
                        }
                    });
                    if (!partModelAttribute.getName().isEmpty() && !partModelAttribute.getType().isEmpty() && !partModelAttribute.getValue().isEmpty()) {
                        PartModelAttribute partModelAttributeCreated = partModelAttributeRepository.save(partModelAttribute);
                        partModelAttributeRepository.refreshMaterializedView();
                        partModelAttributes.add(partModelAttributeCreated);
                    }
                }
        );

        return partModelAttributes;
    }

    @Override
    @Transactional
    public void addPartModelToVehicleModel(AddPartModelRequestDto addPartModelRequestDto) throws Throwable {
        CustomOrganisation partModelManufacturer = Optional.ofNullable(organisationRepository.findByName(addPartModelRequestDto.getPartModelManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("PART_MODEL_MFR_NOT_FOUND", addPartModelRequestDto.getPartModelManufacturerName())));
        CustomOrganisation vehicleModelManufacturer = Optional.ofNullable(organisationRepository.findByName(addPartModelRequestDto.getVehicleModelManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("VEHICLE_MODEL_MFR_NOT_FOUND", addPartModelRequestDto.getVehicleModelManufacturerName())));
        PartModel partModel = (PartModel) modelRepository.findByNameAndManufacturer(addPartModelRequestDto.getPartModelName(), partModelManufacturer).orElseThrow(() -> new PartException(evMessageBundle.getMessage("PART_MODEL_NOT_FOUND_WITH_NAME_AND_MFR_INFO", addPartModelRequestDto.getPartModelName(), addPartModelRequestDto.getPartModelManufacturerName())));
        VehicleModel vehicleModel = (VehicleModel) modelRepository.findByNameAndManufacturer(addPartModelRequestDto.getVehicleModelName(), vehicleModelManufacturer).orElseThrow(() -> new PartException(evMessageBundle.getMessage("VEHICLE_MODEL_NOT_FOUND_WITH_NAME_AND_MFR_INFO", addPartModelRequestDto.getVehicleModelName(), addPartModelRequestDto.getVehicleModelManufacturerName())));

        vehicleModel.getPartModels().add(partModel);
        modelRepository.save(vehicleModel);
    }


    @Override
    @Transactional
    public void addPartModelAttributes(AddPartModelAttributesDto addPartModelAttributesDto) throws Throwable {
        CustomOrganisation partModelManufacturer = Optional.ofNullable(organisationRepository.findByName(addPartModelAttributesDto.getPartModelManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("PART_MODEL_MFR_NOT_FOUND", addPartModelAttributesDto.getPartModelManufacturerName())));
        PartModel partModel = (PartModel) modelRepository.findByNameAndManufacturer(addPartModelAttributesDto.getPartModelName(), partModelManufacturer).orElseThrow(() -> new PartException(evMessageBundle.getMessage("PART_MODEL_DO_NOT_EXISTS_WITH_PART_MODEL_NAME_AND_MFR", addPartModelAttributesDto.getPartModelName(), addPartModelAttributesDto.getPartModelManufacturerName())));
        partModel.setPartModelAttributes(getPartModelAttributes(addPartModelAttributesDto.getAttributes(), partModel.getPartModelAttributes()));
        modelRepository.save(partModel);
    }

    @Override
    public List<PartModelAttributeDto> findPartModelAttributes(Long partModelId) throws Throwable {
        if (!modelRepository.existsById(partModelId)) {
            throw new PartModelException(evMessageBundle.getMessage("PART_MODEL_ID_NOT_FOUND", partModelId));
        }
        List<PartModelAttributeProjection> partModelAttributeProjections = partModelAttributeRepository.findPartModelAttributes(partModelId);
        return partModelAttributeProjections.stream().map(e -> new PartModelAttributeDto(
                e.getName(), e.getValue(), e.getModelId(), e.getType()
        )).toList();
    }

    @Override
    public PartModelTreeDto getPartModelTreeView(Long id) {
        //This basic version will work for 1 level i.e. vehicle model-part model association. To be improved after part model-part model association is done
        PartModel partModel = partModelRepository.findById(id).orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("PART_MODEL_ID_NOT_FOUND", id)));
        List<PartModel> associatedPartModels = partModelRepository.findAllById(partModelRepository.findAssociatedPartModels(id));
        Map<PartType, List<PartModelTreeDto>> partModelTreeDtos = new HashMap<>();
        associatedPartModels.forEach(e ->
                partModelTreeDtos.computeIfAbsent(e.getPartType(), key -> new ArrayList<>())
                        .add(new PartModelTreeDto(e.getId(), e.getPartType(), e.getName(), new HashMap<>()))
        );
        return new PartModelTreeDto(partModel.getId(), partModel.getPartType(), partModel.getName(), partModelTreeDtos);
    }


    @Override
    @Transactional
    public void convertAndSaveMotor(MotorModelDto modelDto) {

        if (!modelDto.getPartType().equals(PartType.MOTOR.name())) {
            throw new PartException(evMessageBundle.getMessage("PROVIDE_PART_TYPE_MOTOR"));
        }

        CustomOrganisation manufacturer = Optional.ofNullable(organisationRepository.findByName(modelDto.getManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));

        Optional<PartModel> partModel = modelDto.getParentPartModelId()
                .map(e -> partModelRepository.findById(e)
                        .orElseThrow(() -> new PartException(evMessageBundle.getMessage("PARENT_ID_NOT_FOUND"))));
        McuModel model = new McuModel();
        model.setName(modelDto.getName());
        model.setPartType(PartType.valueOf(modelDto.getPartType()));
        model.setDescription(modelDto.getDescription());
        model.setParentPartModelId(partModel.orElse(null));
        model.setManufacturer(manufacturer);

        Set<PartModelAttribute> partModelAttributes = new HashSet<>();

        if (modelDto.getAttributes() != null) {
            modelDto.getAttributes().forEach((key, value) -> {
                if (value != null) {
                    String dataType = determineDataType(value);
                    partModelAttributes.add(new PartModelAttribute(key, dataType, value.toString()));
                }
            });
        }

        model.setPartModelAttributes(partModelAttributes);
        partModelRepository.save(model);
    }

    private String determineDataType(Object value) {
        if (value instanceof Number) {
            return "FLOAT";
        } else if (value instanceof Boolean) {
            return "BOOLEAN";
        } else {
            return "STRING";
        }
    }

    @Override
    public List<MotorModelDto> findAllMotor(Pageable pageable) {

        return partModelRepository.findAllByPartType(pageable, PartType.MOTOR).
                stream()
                .filter(e -> e.getPartType().equals(PartType.MOTOR))
//                .map(e -> (McuModel) e)
                .map(e -> {
                            MotorModelDto modelModelDto = new MotorModelDto();
                            modelModelDto.setId(e.getId());
                            modelModelDto.setPartType(e.getPartType().name());
                            modelModelDto.setName(e.getName());
                            modelModelDto.setDescription(e.getDescription());
                            modelModelDto.setParentPartModelId(Optional.ofNullable(e.getParentPartModelId()).stream().map(x -> x.getId()).findAny());
                            modelModelDto.setManufacturerName(Optional.ofNullable(e.getManufacturer()).isPresent() ? e.getManufacturer().getOrganisationProfile().getName() : null);

                            Map<String, Object> attributesMap = new HashMap<>();
                            e.getPartModelAttributes().forEach(attr -> {
                                String key = attr.getName();
                                String value = attr.getValue();
                                String type = attr.getType();

                                switch (type) {
                                    case "FLOAT":
                                        attributesMap.put(key, Float.parseFloat(value));
                                        break;
                                    case "INTEGER":
                                        attributesMap.put(key, Integer.parseInt(value));
                                        break;
                                    case "STRING":
                                    default:
                                        attributesMap.put(key, value);
                                        break;
                                }
                            });

                            modelModelDto.setAttributes(attributesMap);
                            return modelModelDto;
                        }
                )
                .collect(Collectors.toList());
    }

    @Autowired
    CustomOrganisationRepository organisationRepository;

    @Override
    @Transactional
    public VehicleModel convertAndSaveVehicleModel(VehicleModelDto vehicleModelDto) {
        if (!vehicleModelDto.getPartType().equals(PartType.VEHICLE.name())) {
            throw new PartException(evMessageBundle.getMessage("PROVIDE_PART_TYPE_MOTOR"));
        }

        log.info("Data is {}", vehicleModelDto);
        CustomOrganisation manufacturer = Optional.ofNullable(organisationRepository.findByName(vehicleModelDto.getManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));

        if (Optional.ofNullable(vehicleModelDto.getName()).isPresent()
                && Optional.ofNullable(vehicleModelDto.getManufacturerName()).isPresent()) {
            Optional<VehicleModel> vehicleModelOpts = vehicleModelRepository
                    .findByNameIgnoreCaseAndManufacturer(
                            vehicleModelDto.getName(), manufacturer);

            if (vehicleModelOpts.isPresent()) {
                return vehicleModelOpts.get();
            }
        }

        //insert

        VehicleModel vehicleModel = new VehicleModel();
        vehicleModel.setModelNo(vehicleModelDto.getModelNo());
        vehicleModel.setName(vehicleModelDto.getName());
        vehicleModel.setPartType(PartType.valueOf(vehicleModelDto.getPartType()));
        vehicleModel.setDescription(vehicleModelDto.getDescription());

        if (Optional.ofNullable(vehicleModelDto.getWeightUom()).isPresent()) {
            vehicleModel.setWeightUom(vehicleModelDto.getWeightUom());
        }
        if (Optional.ofNullable(vehicleModelDto.getNetWeight()).isPresent()) {
            vehicleModel.setNetWeight(vehicleModelDto.getNetWeight());
        }

        if (Optional.ofNullable(vehicleModelDto.getGrossWeight()).isPresent()) {
            vehicleModel.setGrossWeight(vehicleModelDto.getGrossWeight());
        }


        vehicleModel.setManufacturer(manufacturer);

        vehicleModelRepository.save(vehicleModel);
        log.info("Vehicle model saved");
        if (Optional.ofNullable(vehicleModelDto.getPartModels()).isPresent()) {
            Set<PartModel> modelParts = vehicleModelDto.getPartModels().stream()
                    .map(e -> {
                        CustomOrganisation partManufacturer = Optional.ofNullable(organisationRepository.findByName(e.getManufacturerName()))
                                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
                        return partModelRepository.findByNameIgnoreCaseAndManufacturer(e.getName(), partManufacturer);
                    })
                    .filter(e -> e.isPresent())
                    .map(e -> e.get())
                    .collect(Collectors.toSet());
            vehicleModel.setPartModels(modelParts);
            vehicleModelRepository.save(vehicleModel);
            eventPublisher.publishEvent(new VehicleModelCreatedEvent(this, vehicleModel));
        }
        return vehicleModel;
    }

    @Override
    public List<VehicleModelDto> find(Optional<Long> id, Pageable pageable) {
        List<VehicleModelDto> vehicleModelDtos = null;
        if (id.isPresent()) {
            vehicleModelDtos = convertVehicleModelEntityToDto(
                    vehicleModelRepository.findById(id.get()).stream());
        } else {
            vehicleModelDtos = convertVehicleModelEntityToDto(
                    vehicleModelRepository.findAll(pageable).stream());
        }
        return vehicleModelDtos;
    }

    @Override
    @Transactional
    public void covertAndSaveColor(ColorModelDto colorModelDto) {
        if (!colorModelDto.getPartType().equals(PartType.COLOR.name())) {
            throw new PartException(evMessageBundle.getMessage("PROVIDE_PART_TYPE_COLOR"));
        }
        CustomOrganisation manufacturer = Optional.ofNullable(organisationRepository.findByName(colorModelDto.getManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("MFR_NOT_FOUND")));

        Optional<PartModel> partModel = colorModelDto.getParentPartModelId()
                .map(e -> partModelRepository.findById(e)
                        .orElseThrow(() -> new PartException(evMessageBundle.getMessage("PARENT_ID_NOT_FOUND"))));
        ColorModel colorModel = new ColorModel();
        colorModel.setName(colorModelDto.getName());
        colorModel.setPartType(PartType.COLOR);
        colorModel.setManufacturer(manufacturer);
        colorModel.setParentPartModelId(partModel.get());
        colorModel.setDescription(colorModelDto.getDescription());
        colorModel.setPartModelAttributes(Set.of(new PartModelAttribute("colorHexCode", "STRING", colorModelDto.getColorHexCode())));

        modelRepository.save(colorModel);
    }

    @Override
    public List<PartModelDto> findAll(Optional<Boolean> isMfr, Optional<PartType> partType, Pageable pageable) {
        Organisation org = organisationRepository.findById(userAuthToken.getCurrentOrgId())
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
        List<PartModel> models;
        Boolean mfr = isMfr.orElse(false);

        if (isMfr.orElse(true) && partType.isEmpty()) {
            models = partModelRepository.findByManufacturer(org,pageable).getContent();
        } else if (!isMfr.orElse(false) && partType.isPresent()) {
            models = partModelRepository.findByPartType(partType.get(),pageable).getContent();
        } else if (isMfr.orElse(false) && partType.isPresent()) {
            models = partModelRepository.findByManufacturerAndPartType(org, partType.get(),pageable).getContent();
        } else {
            models = partModelRepository.findAll(pageable).getContent();
        }
        return models.stream()
                .map(e -> {
                    PartModelDto dto = new PartModelDto(
                            e.getPartType().name(),
                            e.getName(),
                            e.getDescription(),
                            e.getParentPartModelId() != null ? e.getParentPartModelId().getId() : null,
                            e.getManufacturer() != null ? e.getManufacturer().getOrganisationProfile().getName() : null,
                            null
                    );
                    dto.setPartId(e.getId());
                    return dto;
                }).toList();
    }
    public PartModelDto findById(Long partModelId) {
        PartModel data = partModelRepository.findById(partModelId)
                .orElseThrow(() ->  new PartException(evMessageBundle.getMessage("PART_MODEL_NOT_FOUND")));

        PartModelDto dto = new PartModelDto(

                data.getPartType().name(),
                data.getName(),
                data.getDescription(),
                data.getParentPartModelId() != null ? data.getParentPartModelId().getId() : null,
                data.getManufacturer() != null ? data.getManufacturer().getOrganisationProfile().getName() : null,
                null
        );
        dto.setPartId(data.getId());
        return dto;
    }

    @Override
    public OrganisationDto findPartManufacturer(Long partModelId) {
        PartModel partModel = partModelRepository.findById(partModelId)
                .orElseThrow(() -> new PartException(evMessageBundle.getMessage("PARENT_ID_NOT_FOUND")));
        CustomOrganisation manufacturer = Optional.ofNullable(organisationRepository.findByName(partModel.getManufacturer().getOrganisationProfile().getName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("MFR_NOT_FOUND")));
        return organisationDtoMapper.entityToDto(manufacturer);
    }

    private List<VehicleModelDto> convertVehicleModelEntityToDto(Stream<VehicleModel> entities) {
        return entities
                .map(e -> new VehicleModelDto(e.getId(), e.getName()
                        , e.getModelNo()
                        , e.getPartModels()
                        .stream()
                        .map(x -> new PartModelDto(x.getPartType().name()
                                , x.getName()
                                , x.getDescription()
                                , Optional
                                .ofNullable(x.getParentPartModelId()).isPresent() ? x.getParentPartModelId().getId() : null, e.getManufacturer().getOrganisationProfile().getEmail(), null)
                        ).collect(Collectors.toSet()),
                        e.getWeightUom(), e.getGrossWeight(), e.getNetWeight()
                        , e.getColorImages().values().stream().flatMap(Set::stream).map(value -> imageRepository.findById(value).get()).map(value -> new ImageDto(value.getTag(), value.getUrl())).collect(Collectors.toSet())
                        , e.getManufacturer().getOrganisationProfile().getName(), e.getDescription(), e.getPartType().toString()))
                .collect(Collectors.toList());
    }


    @Override
    @Transactional
    public void saveAndUpdateDriveModeMaxRange(DriveModelMaxRangeDto driveModelMaxRangeDto) {
        DriveModeMaxRange driveModeMaxRange = new DriveModeMaxRange();
        CustomOrganisation vehicleManufacturer = Optional.ofNullable(organisationRepository.findByName(driveModelMaxRangeDto.getVehicleManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND_WITH_ORG_INFO", " with manufacturer name " + driveModelMaxRangeDto.getVehicleManufacturerName())));
        VehicleModel vehicleModel = vehicleModelRepository.findByNameIgnoreCaseAndManufacturer(driveModelMaxRangeDto.getVehicleModelName(), vehicleManufacturer).orElseThrow(() -> new VehicleNotFoundException(evMessageBundle.getMessage("VEHICLE_MODEL_NOT_FOUND", driveModelMaxRangeDto.getVehicleModelName())));


        CustomOrganisation batteryModelManufacturer = Optional.ofNullable(organisationRepository.findByName(driveModelMaxRangeDto.getBatteryManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND_WITH_ORG_INFO", " with manufacturer name " + driveModelMaxRangeDto.getBatteryManufacturerName())));
        PartModel batteryModel = partModelRepository.findByNameIgnoreCaseAndManufacturer(driveModelMaxRangeDto.getBatteryModelName(), batteryModelManufacturer).orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("PART_MODEL_NOT_FOUND_WITH_NAME_AND_MFR_INFO", driveModelMaxRangeDto.getBatteryModelName(), driveModelMaxRangeDto.getBatteryManufacturerName())));

        CustomOrganisation driveModeOrg = Optional.ofNullable(organisationRepository.findByName(driveModelMaxRangeDto.getVehicleManufacturerName()))
                .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND_WITH_ORG_INFO", " with manufacturer name " + driveModelMaxRangeDto.getDriveModeOrgName())));

        DriveMode driveMode = Optional.ofNullable(driveModeRepository.findByNameAndOrganisation(driveModelMaxRangeDto.getDriveModeName(), driveModeOrg)).orElseThrow(() -> new DriveModeException(evMessageBundle.getMessage("DRIVE_MODE_NOT_FOUND_WITH_DRIVE_MODE_INFO", driveModelMaxRangeDto.getDriveModeName())));
        Optional<DriveModeMaxRange> driveModeMaxRangeOptional = driveModeMaxRangeRepository.findByVehicleModelAndDriveModeAndBatteryModel(vehicleModel, driveMode, batteryModel);

        if (driveModeMaxRangeOptional.isPresent()) {
            driveModeMaxRange = driveModeMaxRangeOptional.get();
            driveModeMaxRange.setMaxRange(driveModelMaxRangeDto.getMaxRange());
        } else {
            driveModeMaxRange = new DriveModeMaxRange(vehicleModel, batteryModel, driveMode, driveModelMaxRangeDto.getMaxRange());
        }
        driveModeMaxRangeRepository.save(driveModeMaxRange);

        log.info("drive mode max range created or modified successfully");
    }
}

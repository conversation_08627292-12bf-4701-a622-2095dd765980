package com.nichesolv.evahanam.evApp.repository;

import com.nichesolv.evahanam.evApp.jpa.EmergencyType;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface EmergencyTypeRepository extends JpaRepository<EmergencyType, Long> {

    List<EmergencyType> findByOrganisation(CustomOrganisation manufacturer);

    Optional<EmergencyType> findByDescriptionAndOrganisation(String emergencyType, CustomOrganisation manufacturer);
}
package com.nichesolv.evahanam.evApp.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.evApp.dto.EmergencyTypeDto;
import com.nichesolv.evahanam.evApp.dto.EmergencyTypeRequestDto;
import com.nichesolv.evahanam.evApp.service.EmergencyService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/emergency")
@SecurityRequirement(name = "Bearer Authentication")
public class EmergencyController {

    @Autowired
    EmergencyService emergencyService;

    @GetMapping
    @ReadOnly
    public List<String> getEmergencyTypes(@RequestAttribute(value = "identifier") String identifier,
                                          @RequestParam(value = "vIdVal") String idValue) {
      return emergencyService.getEmergencyTypes(identifier);
    }

    @GetMapping("/{emergencyTypeId}")
    @ReadOnly
    public EmergencyTypeDto getEmergencyTypeId(@PathVariable Long emergencyTypeId) {
        return emergencyService.getEmergencyTypeId(emergencyTypeId);
    }

    @PostMapping
    public void saveEmergencyType(@RequestBody EmergencyTypeRequestDto emergencyTypeRequestDto) {
        emergencyService.saveEmergencyType(emergencyTypeRequestDto);
    }

}

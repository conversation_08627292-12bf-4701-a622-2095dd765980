package com.nichesolv.evahanam.evApp.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.evApp.dto.EmergencyEventRequestDto;
import com.nichesolv.evahanam.evApp.dto.EmergencyEventResponseDto;
import com.nichesolv.evahanam.evApp.jpa.EmergencyEvent;
import com.nichesolv.evahanam.evApp.service.EmergencyService;
import com.nichesolv.evahanam.vehicleTests.dto.EmergencyEventDto;
import com.nichesolv.nds.model.user.CustomUser;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;


@Slf4j
@RestController
@RequestMapping("/emergency-event")
@SecurityRequirement(name = "Bearer Authentication")
public class EmergencyEventController {

    @Autowired
    EmergencyService emergencyService;

    @PostMapping
    public EmergencyEventResponseDto createEmergencyEvent(@AuthenticationPrincipal CustomUser user,
                                                          @Valid @RequestBody EmergencyEventRequestDto request) {
        return emergencyService.createEmergencyEvent(user, request);
    }

    @GetMapping("/{emergencyEventId}")
    @ReadOnly
    public EmergencyEventDto getEmergencyEventById(@PathVariable Long emergencyEventId) {
        return emergencyService.getEmergencyEventById(emergencyEventId);
    }



}
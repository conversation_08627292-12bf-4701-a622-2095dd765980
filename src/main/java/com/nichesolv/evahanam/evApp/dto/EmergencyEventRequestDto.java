package com.nichesolv.evahanam.evApp.dto;


import com.nichesolv.evahanam.evApp.jpa.EmergencyType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmergencyEventRequestDto {

    @NotNull(message = "User latitude is required")
    Float userLatitude;

    @NotNull(message = "User longitude is required")
    Float userLongitude;

    @NotNull(message = "User altitude is required")
    Float userAltitude;

    String additionalInfo;

    @NotBlank(message = "Emergency type is required")
    String emergencyType;

    @NotBlank(message = "{IMEI_VALIDATION}")
    String imei;

}

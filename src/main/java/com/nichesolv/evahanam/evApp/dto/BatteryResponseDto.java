package com.nichesolv.evahanam.evApp.dto;

import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class BatteryResponseDto {
    private String avgTimeForFullCharge;   // calculated avg
    private Map<String, HealthDetail> health;   // Excellent, Normal, Critical etc.
    private Map<String, AlertOrAlarmDetail> alerts;  // alert1, alert2...
    private Map<String, AlertOrAlarmDetail> alarms;  // alarm1, alarm2...

    @Data
    public static class HealthDetail {
        private int value;             // % or count
        private List<Long> imei;       // vehicles under this health category
    }

    @Data
    public static class AlertOrAlarmDetail {
        private int value;             // count or percentage
        private List<Long> imei;       // vehicles under this alert/alarm
    }
}

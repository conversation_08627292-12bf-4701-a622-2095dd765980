package com.nichesolv.evahanam.evApp.service;

import com.nichesolv.evahanam.evApp.dto.EmergencyEventRequestDto;
import com.nichesolv.evahanam.evApp.dto.EmergencyEventResponseDto;
import com.nichesolv.evahanam.evApp.dto.EmergencyTypeDto;
import com.nichesolv.evahanam.evApp.dto.EmergencyTypeRequestDto;
import com.nichesolv.evahanam.vehicleTests.dto.EmergencyEventDto;
import com.nichesolv.nds.model.user.CustomUser;
import jakarta.validation.Valid;

import java.util.List;

public interface EmergencyService {
    List<String> getEmergencyTypes(String identifier);

    void saveEmergencyType(EmergencyTypeRequestDto emergencyTypeRequestDto);

    EmergencyEventResponseDto createEmergencyEvent(CustomUser user , EmergencyEventRequestDto request);

    EmergencyEventDto getEmergencyEventById(Long emergencyEventId);

    EmergencyTypeDto getEmergencyTypeId(Long emergencyTypeId);
}

package com.nichesolv.evahanam.evApp.jpa;

import com.nichesolv.evahanam.evApp.enums.EmergencyStatus;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.locationtech.jts.geom.Point;

import java.time.Instant;
import java.util.List;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "emergency_event")
public class EmergencyEvent {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "emergency_event_id_seq")
    @SequenceGenerator(name = "emergency_event_id_seq", sequenceName = "emergency_event_id_seq", allocationSize = 1)
    Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_id", foreignKey = @ForeignKey(name = "fk_emergency_event_vehicle_id"))
    @NotNull
    Vehicle vehicle;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(name = "fk_emergency_event_user_id"))
    @NotNull
    CustomUser user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mfr_org_id", foreignKey = @ForeignKey(name = "fk_emergency_event_mfr_org_id"))
    @NotNull
    CustomOrganisation mfrOrg;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "emergency_type_id", foreignKey = @ForeignKey(name = "fk_emergency_event_emergency_type_id"))
    @NotNull
    EmergencyType emergencyType;

    @Column(name = "vehicle_last_location", columnDefinition = "geography(PointZM,4326)")
    Point vehicleLastLocation;

    @Column(name = "user_location", columnDefinition = "geography(PointZM,4326)")
    Point userLocation;

    String text;

    @Enumerated(EnumType.STRING)
    @NotNull
    EmergencyStatus status;

    @CreationTimestamp
    Instant registeredOn;

    Instant fixedOn;

    @OneToMany(mappedBy = "emergencyEvent")
    List<EmergencyEventAssignmentHistory> assignmentHistory;

}
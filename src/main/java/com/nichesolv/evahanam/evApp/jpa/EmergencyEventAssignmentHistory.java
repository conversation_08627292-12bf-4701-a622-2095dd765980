package com.nichesolv.evahanam.evApp.jpa;

import com.nichesolv.nds.model.user.CustomUser;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.Instant;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "emergency_event_assignment_history")
public class EmergencyEventAssignmentHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "emergency_event_assignment_history_id_seq")
    @SequenceGenerator(name = "emergency_event_assignment_history_id_seq", sequenceName = "emergency_event_assignment_history_id_seq", allocationSize = 1)
    Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "emergency_event_id", foreignKey = @ForeignKey(name = "fk_assignment_history_emergency_event_id"))
    @NotNull
    EmergencyEvent emergencyEvent;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assigned_to", foreignKey = @ForeignKey(name = "fk_assignment_history_assigned_to"))
    @NotNull
    CustomUser assignedTo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assigned_by", foreignKey = @ForeignKey(name = "fk_assignment_history_assigned_by"))
    @NotNull
    CustomUser assignedBy;

    @CreationTimestamp
    Instant assignedOn;

    String comment;
}
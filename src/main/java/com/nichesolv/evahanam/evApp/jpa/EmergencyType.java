package com.nichesolv.evahanam.evApp.jpa;

import com.nichesolv.nds.model.organisation.CustomOrganisation;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "emergency_type", indexes = {
        @Index(name = "idx_emergency_type_organisation_id", columnList = "organisation_id")
})
public class EmergencyType {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "emergency_type_id_seq")
    @SequenceGenerator(name = "emergency_type_id_seq", sequenceName = "emergency_type_id_seq", allocationSize = 1)
    Long id;

    @JoinColumn(foreignKey = @ForeignKey(name = "fk_emergency_type_organisation_id"))
    @ManyToOne(targetEntity = CustomOrganisation.class)
    CustomOrganisation organisation;

    @NotBlank
    String description;

    public EmergencyType(String description, CustomOrganisation organisation) {
        this.description = description;
        this.organisation = organisation;
    }
}

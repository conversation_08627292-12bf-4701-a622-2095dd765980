package com.nichesolv.evahanam.charging.listener;

import com.nichesolv.evahanam.charging.enums.ChargingStatus;
import com.nichesolv.evahanam.charging.service.ChargingEventService;
import com.nichesolv.evahanam.common.events.VehicleStateEvent;
import com.nichesolv.evahanam.trip.jpa.VehicleEventMonitor;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
@SpringBootTest
public class ChargeEventListener {

    @Autowired
    ApplicationEventPublisher eventPublisher;

    @SpyBean
    ChargingEventService chargingService;

    @Autowired
    IVehicleService vehicleService;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;




    @Test
    @Transactional
    @Rollback
    void testEventDurationStartLimitReached() {


        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        int eventSize = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.IN_PROGRESS);
        assertThat(eventSize).isEqualTo(0);
        log.info("Charge Event size before event: {}", eventSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(0L);
        monitor.setStoppageTime(0L);
        monitor.setChargingTime(60l);
        monitor.setRunningTimeLastUpdatedOn(null);
        monitor.setStoppageTimeLastUpdatedOn(null);
        monitor.setChargingTimeLastUpdatedOn(Instant.now());


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        status.setVehicleState(VehicleState.CHARGING);
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        eventPublisher.publishEvent(event);
        int eventSizeNew = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.IN_PROGRESS);
        log.info("Charge Event size after event publish: {}", eventSizeNew);
        assertThat(
                eventSizeNew
        ).isGreaterThan(eventSize);
    }

    @Test
    @Transactional
    @Rollback
    void testEventDurationStartLimitNotReached() {


        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        int eventSize = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.IN_PROGRESS);
        assertThat(eventSize).isEqualTo(0);
        log.info("Charge Event size before event: {}", eventSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(0L);
        monitor.setStoppageTime(0L);
        monitor.setChargingTime(50l);
        monitor.setRunningTimeLastUpdatedOn(null);
        monitor.setStoppageTimeLastUpdatedOn(null);
        monitor.setChargingTimeLastUpdatedOn(Instant.now());


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        status.setVehicleState(VehicleState.CHARGING);
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        eventPublisher.publishEvent(event);
        int eventSizeNew = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.IN_PROGRESS);
        log.info("Charge Event size after event publish: {}", eventSizeNew);
        assertThat(
                eventSizeNew
        ).isEqualTo(eventSize);
    }

    @Test
    @Transactional
    @Rollback
    void testEventDurationEndLimitNotReached() {


        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        testEventDurationStartLimitReached();
        int inProgressEventSize = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.IN_PROGRESS);
        int completedEventSize = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.COMPLETED);
        assertThat(inProgressEventSize).isEqualTo(1);
        log.info("Charge Event stats before event publish: inProgress:{} ,completed:{}", inProgressEventSize,completedEventSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(0L);
        monitor.setStoppageTime(500L);
        monitor.setChargingTime(0l);
        monitor.setRunningTimeLastUpdatedOn(null);
        monitor.setStoppageTimeLastUpdatedOn(Instant.now());
        monitor.setChargingTimeLastUpdatedOn(null);


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        status.setVehicleState(VehicleState.STOPPED);
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        eventPublisher.publishEvent(event);
        int inProgressEventSizeNew = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.IN_PROGRESS);
        int completedEventSizeNew = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.COMPLETED);
        log.info("Charge Event stats after event publish: inProgress:{} ,completed:{}", inProgressEventSizeNew,completedEventSizeNew);
        assertThat(
                completedEventSizeNew
        ).isEqualTo(completedEventSize);

        assertThat(inProgressEventSize).isEqualTo(inProgressEventSizeNew);
    }



    @Test
    @Transactional
    @Rollback
    void testEventDurationEndLimitReached() {
        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        testEventDurationStartLimitReached();
        int inProgressEventSize = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.IN_PROGRESS);
        int completedEventSize = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.COMPLETED);
        assertThat(inProgressEventSize).isEqualTo(1);
        log.info("Charge Event stats before event publish: inProgress:{} ,completed:{}", inProgressEventSize,completedEventSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(0L);
        monitor.setStoppageTime(603L);
        monitor.setChargingTime(0l);
        monitor.setRunningTimeLastUpdatedOn(null);
        monitor.setStoppageTimeLastUpdatedOn(Instant.now());
        monitor.setChargingTimeLastUpdatedOn(null);


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        status.setVehicleState(VehicleState.STOPPED);
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        eventPublisher.publishEvent(event);
        int inProgressEventSizeNew = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.IN_PROGRESS);
        int completedEventSizeNew = chargingService.countByVehicleAndStatus(vehicle, ChargingStatus.COMPLETED);
        log.info("Charge Event stats after event publish: inProgress:{} ,completed:{}", inProgressEventSizeNew,completedEventSizeNew);
        assertThat(
                completedEventSizeNew
        ).isGreaterThan(completedEventSize);

        assertThat(inProgressEventSize).isGreaterThan(inProgressEventSizeNew);
    }
}


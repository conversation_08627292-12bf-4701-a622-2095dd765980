apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dev-alb-ingress-ev-server
  namespace: dev
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/group.name: dev-alb-ingress-group
    alb.ingress.kubernetes.io/group.order: '2'
    alb.ingress.kubernetes.io/certificate-arn: |
            arn:aws:acm:ap-south-1:296710535627:certificate/fb216f04-4d0f-4992-826f-b85f7714c6e8
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/load-balancer-name: eks-dev
spec:
  ingressClassName: alb      
  rules:
    - host: nichesolv.eks-be-dev.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090

    - host: nds.eks-be-dev.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: nds-dealer.eks-be-dev.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: lml.eks-be-dev.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: simpson.eks-be-dev.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090

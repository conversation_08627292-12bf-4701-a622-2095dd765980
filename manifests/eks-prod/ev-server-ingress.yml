apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: prod-alb-ingress-ev-server
  namespace: prod
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/group.name: prod-alb-ingress-group
    alb.ingress.kubernetes.io/group.order: '2'
    alb.ingress.kubernetes.io/certificate-arn: |
            arn:aws:acm:ap-south-1:296710535627:certificate/f6d1b885-33ec-4ac7-a3c8-bb8ee1951e40
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/load-balancer-name: eks-prod
spec:
  ingressClassName: alb      
  rules:
    - host: nichesolv.ev-be.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090

    - host: nds.ev-be.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: nds-dealer.ev-be.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: lml.ev-be.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: simpson.ev-be.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ev-server
  namespace: prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ev-server
  template:
    metadata:
      labels:
        app: ev-server
    spec:
      affinity:
      containers:
        - name: ev-server
          image: 296710535627.dkr.ecr.ap-south-1.amazonaws.com/eks-prod-ev-datalayer-repo:latest
          ports:
            - containerPort: 8080
          volumeMounts:
            - name: logs-volume
              mountPath: /logs
          envFrom:
            - configMapRef:
                name: ev-server-configmap
            - secretRef:
                name: ev-server-secrets
          readinessProbe:
            httpGet:
              path: /ev/swagger-ui/index.html
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
          # resources:
          #   requests:
          #     cpu: "500m"      
          #     memory: "512Mi"  
          #   limits:
          #     cpu: "1"         
          #     memory: "1Gi"     

      volumes:
        - name: logs-volume
          emptyDir: { }
      imagePullSecrets:
        - name: regcred
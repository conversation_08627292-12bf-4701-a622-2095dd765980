apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: preprod-alb-ingress-ev-server
  namespace: preprod
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/group.name: preprod-alb-ingress-group
    alb.ingress.kubernetes.io/group.order: '2'
    alb.ingress.kubernetes.io/certificate-arn: |
            arn:aws:acm:ap-south-1:296710535627:certificate/2f23915c-a68d-4df2-b164-646cd338a75e
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/load-balancer-name: eks-preprod
spec:
  ingressClassName: alb      
  rules:
    - host: nichesolv.ev-be-preprod.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090

    - host: nds.ev-be-preprod.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: nds-dealer.ev-be-preprod.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: lml.ev-be-preprod.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: simpson.ev-be-preprod.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090

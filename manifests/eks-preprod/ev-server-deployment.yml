apiVersion: apps/v1
kind: Deployment
metadata:
  name: ev-server
  namespace: preprod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ev-server
  template:
    metadata:
      labels:
        app: ev-server
    spec:
      affinity:
        # nodeAffinity:
        #   requiredDuringSchedulingIgnoredDuringExecution:
        #     nodeSelectorTerms:
        #     - matchExpressions:
        #       - key: type
        #         operator: In
        #         values:
        #         - dbnode
        # podAntiAffinity:
        #   requiredDuringSchedulingIgnoredDuringExecution:
        #     - labelSelector:
        #         matchLabels:
        #           app: timescale-preprod
        #           role: master
        #       topologyKey: "kubernetes.io/hostname"

        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: type
                operator: In
                values:
                - dbnode
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchLabels:
                  app: timescale-preprod
                  role: master
              topologyKey: "kubernetes.io/hostname"      
      containers:
        - name: ev-server
          image: 296710535627.dkr.ecr.ap-south-1.amazonaws.com/eks-preprod-ev-datalayer-repo:latest
          ports:
            - containerPort: 8080
          volumeMounts:
            - name: logs-volume
              mountPath: /logs
          envFrom:
            - configMapRef:
                name: ev-server-configmap
            - secretRef:
                name: ev-server-secrets
          readinessProbe:
            httpGet:
              path: /ev/swagger-ui/index.html
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
          # resources:
          #   requests:
          #     cpu: "500m"      
          #     memory: "512Mi"  
          #   limits:
          #     cpu: "1"         
          #     memory: "1Gi"     

      volumes:
        - name: logs-volume
          emptyDir: { }
      imagePullSecrets:
        - name: regcred

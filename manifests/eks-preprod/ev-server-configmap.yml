apiVersion: v1
kind: ConfigMap
metadata:
  name: ev-server-configmap
  namespace: preprod
data:
  POSTGRES_DB: 'evahana'
  POSTGRES_HOST_URI: 'timescale-preprod.preprod.svc.cluster.local:5432'
  POSTGRES_REPLICA_HOST_URI: 'timescale-preprod-replica.preprod.svc.cluster.local:5432'
  AWS_PROFILE: 'default'
  DDL_AUTO: 'update'
  GITHUB_USERNAME: 'ev-nichesolv'
  GOOGLE_OAUTH2_CLIENT_ID: '54947800811-9op619s69vgv78n31kqn6dgpdpn5n38i.apps.googleusercontent.com'
  GOOGLE_OAUTH2_REDIRECT_URI: 'http://localhost:3000/oauth2/redirect'
  JWT_ISSUER: 'nds'
  MAIL_USER: '<EMAIL>'
  INSTANCE_TYPE: 'pre-prod'
  RABBIT_MQ_HOST: 'rabbitmq.default.svc.cluster.local:5672'
  TRIP_DETAILS_INPUT_QUEUE: 'TRIP_DETAILS_INPUT_QUEUE'
  TRIP_DETAILS_OUTPUT_QUEUE: 'TRIP_DETAILS_OUTPUT_QUEUE'
  TELEMETRY_PERSISTENCE_QUEUE: 'TELEMETRY_PERSISTENCE_QUEUE'
  LOCATION_PERSISTENCE_QUEUE: 'LOCATION_PERSISTENCE_QUEUE'
  BATTERY_CELL_PERSISTENCE_QUEUE: 'BATTERY_CELL_PERSISTENCE_QUEUE'
  BATTERY_STACK_PERSISTENCE_QUEUE: 'BATTERY_STACK_PERSISTENCE_QUEUE'
  BATTERY_STATUS_PERSISTENCE_QUEUE: 'BATTERY_STATUS_PERSISTENCE_QUEUE'
  CSV_REQUEST_QUEUE: 'CSV_REQUEST_QUEUE'
  CSV_RESPONSE_QUEUE: 'CSV_RESPONSE_QUEUE'
  TEST_RIDE_ANALYTICS_ENDPOINT: 'http://ev-servables-service.preprod.svc.cluster.local:8000/trip_details'
  TYRE_PRESSURE_ENDPOINT: 'http://ev-servables-service.preprod.svc.cluster.local:8000/tyre_pressure'
  REVERSE_GEOCODING_API: 'http://ev-location-service.preprod.svc.cluster.local:3500/reversegeocoding'
  APPLE_TEST_PHONE_NUMBER: '+************'
  APPLE_TEST_OTP: '957595'
  ADMIN_PASSWORD_RESET_MAIL_ID: '<EMAIL>'
  REDIS_TTL: '0'
  REDIS_LOG_LEVEL: 'DEBUG'
  GRAFANA_BASE_URL: 'https://eks-be-preprod.nichesolv.com/grafana'
  GRAFANA_POSTGRES_DB: 'evahana'
  GRAFANA_DATASOURCE_UID: 'evahana_uid'
  GRAFANA_DATASOURCE_NAME: 'EVahana'
  GRAFANA_DATASOURCE_DB_TYPE_LOGO: 'public/app/plugins/datasource/postgres/img/postgresql_logo.svg'
  GRAFANA_DATASOURCE_DATABASE: 'evahana'
  GRAFANA_DATASOURCE_DB_TYPE: 'postgres'
  GRAFANA_DATASOURCE_DB_TYPE_NAME: 'PostgreSQL'
  GRAFANA_JAVA_ADMIN_USERNAME: 'javaadmin'
  WEBAPP_JWT_EXPIRY: '2592000000'
  MOBAPP_JWT_EXPIRY: '5184000000'
  LOG_LEVEL: 'INFO'
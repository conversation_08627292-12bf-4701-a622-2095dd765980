apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ev-server
  namespace: preprod
  annotations:
    kubernetes.io/ingress.class: "nginx"
spec:
  rules:
    - host: nichesolv.ev-be.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090

    - host: nds.ev-be.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: dealer.ev-be.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
    - host: lml.ev-be.nichesolv.com
      http:
        paths:
          - pathType: Prefix
            path: /ev
            backend:
              service:
                name: ev-server
                port:
                  number: 9090
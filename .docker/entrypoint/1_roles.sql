CREATE DATABASE evahana;
CREATE ROLE "evahanaRoot" LOGIN PASSWORD 'hMEg19cljmQYAJA5';
ALTER DATABASE evahana OWNER TO "evahanaRoot";
GRANT ALL PRIVILEGES ON DATABASE evahana TO "evahanaRoot";

CREATE ROLE esarathi LOGIN PASSWORD 'ifPwd';
GRANT ALL PRIVILEGES ON DATABASE evahana TO esarathi;

CREATE ROLE "qa_user" LOGIN PASSWORD 'ifPwd';
GRANT SELECT ON ALL TABLES IN SCHEMA public TO qa_user;

CREATE DATABASE grafana;
CREATE ROLE devgrafanauser LOGIN PASSWORD 'ifPwd';
ALTER DATABASE grafana OWNER TO "evahanaRoot";
GRANT ALL PRIVILEGES ON DATABASE grafana TO "evahanaRoot";
GRANT ALL PRIVILEGES ON DATABASE grafana TO devgrafanauser;

CREATE DATABASE pocdata;
CREATE ROLE grafanareader LOGIN PASSWORD 'ifPwd';
ALTER DATABASE pocdata OWNER TO "evahanaRoot";
GRANT ALL PRIVILEGES ON DATABASE pocdata TO "evahanaRoot";
GRANT ALL PRIVILEGES ON DATABASE pocdata TO grafanareader;

GRANT ALL PRIVILEGES ON DATABASE evahana TO grafanareader;

CREATE ROLE tripsummariser LOGIN PASSWORD 'ifPwd';
GRANT ALL PRIVILEGES ON DATABASE evahana TO tripsummariser;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO grafanareader;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO devgrafanauser;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO devgrafanauser;

CREATE ROLE "usermgmt" LOGIN PASSWORD 'ifPwd';
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO usermgmt;

CREATE ROLE "pgadminuser" LOGIN PASSWORD 'ifPwd';
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pgadminuser;

CREATE ROLE "superset" LOGIN PASSWORD 'ifPwd';
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO superset;

CREATE ROLE "flinkuser" LOGIN PASSWORD 'ifPwd';
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO superset;

CREATE ROLE "evahanaroot" LOGIN PASSWORD 'ifPwd';
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO superset;

CREATE ROLE "dataanalysis" LOGIN PASSWORD 'ifPwd';
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO dataanalysis;

CREATE ROLE "monitoring" LOGIN PASSWORD 'ifPwd';
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO monitoring;
version: '3.8'

services:
  timescaledb:
    image: timescale/timescaledb-ha:pg14-latest
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5433:5432"
    restart: on-failure
    volumes:
      - ./data:/home/<USER>
      - ./entrypoint/:/docker-entrypoint-initdb.d
      - timescale-data:/home/<USER>/pgdata/data
    networks:
      - escooter

  rabbitmq:
    image: rabbitmq:3.12.1-management
    logging:
      options:
        max-size: "10m"
        max-file: "5"
    environment:
      - RABBITMQ_DEFAULT_USER=user
      - RABBITMQ_DEFAULT_PASS=password
      - RABBITMQ_DEFAULT_VHOST=nds
    ports:
      - "15672:15672"
      - "5672:5672"
    restart: on-failure
    networks:
      - escooter

networks:
  escooter:

volumes:
  timescale-data: